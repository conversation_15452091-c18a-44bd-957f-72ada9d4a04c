#!/bin/bash

# Wolf Fun Docker 镜像清理脚本
# 用于清理不需要的 Docker 镜像、容器和缓存，释放磁盘空间

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🧹 Wolf Fun Docker 清理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --all            清理所有内容（镜像、容器、网络、卷、缓存）"
    echo "  --images         只清理镜像"
    echo "  --containers     只清理停止的容器"
    echo "  --networks       只清理未使用的网络"
    echo "  --volumes        只清理未使用的卷"
    echo "  --cache          只清理构建缓存"
    echo "  --keep-backups N 保留最近 N 个备份镜像（默认：3）"
    echo "  --dry-run        只显示将要清理的内容"
    echo "  --force          强制清理（跳过确认）"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                       # 交互式清理（推荐）"
    echo "  $0 --all --force         # 强制清理所有内容"
    echo "  $0 --images --dry-run    # 预览镜像清理"
    echo "  $0 --keep-backups 5      # 保留5个备份镜像"
}

# 显示磁盘使用情况
show_disk_usage() {
    local title="$1"
    log_info "$title"
    
    # 显示 Docker 系统使用情况
    echo "🐳 Docker 系统使用情况:"
    docker system df
    
    # 显示镜像数量统计
    local total_images=$(docker images -q | wc -l)
    local dangling_images=$(docker images -f "dangling=true" -q | wc -l)
    local kaia_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "moofun-kaia" || echo "0")
    local pharos_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "moofun-pharos" || echo "0")
    
    echo "📊 镜像统计:"
    echo "  总镜像数: $total_images"
    echo "  悬空镜像: $dangling_images"
    echo "  Kaia 镜像: $kaia_images"
    echo "  Pharos 镜像: $pharos_images"
    
    # 显示容器统计
    local total_containers=$(docker ps -a -q | wc -l)
    local running_containers=$(docker ps -q | wc -l)
    local stopped_containers=$((total_containers - running_containers))
    
    echo "📦 容器统计:"
    echo "  总容器数: $total_containers"
    echo "  运行中: $running_containers"
    echo "  已停止: $stopped_containers"
    echo ""
}

# 清理悬空镜像
cleanup_dangling_images() {
    log_step "清理悬空镜像..."
    
    local dangling_images=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling_images" ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "将清理的悬空镜像:"
            echo "$dangling_images" | xargs -I {} docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}" --filter "id={}"
        else
            echo "$dangling_images" | xargs docker rmi 2>/dev/null || true
            log_success "悬空镜像清理完成"
        fi
    else
        log_info "没有悬空镜像需要清理"
    fi
}

# 清理应用镜像备份
cleanup_app_backups() {
    log_step "清理应用镜像备份（保留最近 $KEEP_BACKUPS 个）..."
    
    # 清理 Kaia 备份镜像
    local kaia_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | sort -r | tail -n +$((KEEP_BACKUPS + 1)))
    if [ -n "$kaia_backups" ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "将清理的 Kaia 备份镜像:"
            echo "$kaia_backups"
        else
            echo "$kaia_backups" | xargs -r docker rmi 2>/dev/null || true
            log_info "清理了旧的 Kaia 备份镜像"
        fi
    fi
    
    # 清理 Pharos 备份镜像
    local pharos_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | sort -r | tail -n +$((KEEP_BACKUPS + 1)))
    if [ -n "$pharos_backups" ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "将清理的 Pharos 备份镜像:"
            echo "$pharos_backups"
        else
            echo "$pharos_backups" | xargs -r docker rmi 2>/dev/null || true
            log_info "清理了旧的 Pharos 备份镜像"
        fi
    fi
}

# 清理停止的容器
cleanup_stopped_containers() {
    log_step "清理停止的容器..."
    
    local stopped_containers=$(docker ps -a -f "status=exited" -q)
    if [ -n "$stopped_containers" ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "将清理的停止容器:"
            echo "$stopped_containers" | xargs -I {} docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" --filter "id={}"
        else
            echo "$stopped_containers" | xargs docker rm 2>/dev/null || true
            log_success "停止的容器清理完成"
        fi
    else
        log_info "没有停止的容器需要清理"
    fi
}

# 清理未使用的网络
cleanup_unused_networks() {
    log_step "清理未使用的网络..."
    
    if [ "$DRY_RUN" = true ]; then
        echo "将清理的未使用网络:"
        docker network ls --filter "dangling=true" --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"
    else
        docker network prune -f >/dev/null 2>&1 || true
        log_success "未使用的网络清理完成"
    fi
}

# 清理未使用的卷
cleanup_unused_volumes() {
    log_step "清理未使用的卷..."
    
    if [ "$DRY_RUN" = true ]; then
        echo "将清理的未使用卷:"
        docker volume ls --filter "dangling=true" --format "table {{.Name}}\t{{.Driver}}\t{{.Size}}"
    else
        docker volume prune -f >/dev/null 2>&1 || true
        log_success "未使用的卷清理完成"
    fi
}

# 清理构建缓存
cleanup_build_cache() {
    log_step "清理构建缓存..."
    
    if [ "$DRY_RUN" = true ]; then
        echo "将清理构建缓存"
        docker system df | grep "Build Cache"
    else
        docker builder prune -f >/dev/null 2>&1 || true
        log_success "构建缓存清理完成"
    fi
}

# 交互式清理选择
interactive_cleanup() {
    log_info "🧹 交互式 Docker 清理"
    echo ""
    
    # 显示当前状态
    show_disk_usage "📊 当前磁盘使用情况:"
    
    echo "请选择要清理的内容:"
    echo "1) 悬空镜像"
    echo "2) 应用镜像备份"
    echo "3) 停止的容器"
    echo "4) 未使用的网络"
    echo "5) 未使用的卷"
    echo "6) 构建缓存"
    echo "7) 全部清理"
    echo "0) 退出"
    echo ""
    
    read -p "请输入选择 (0-7): " choice
    
    case $choice in
        1) cleanup_dangling_images ;;
        2) cleanup_app_backups ;;
        3) cleanup_stopped_containers ;;
        4) cleanup_unused_networks ;;
        5) cleanup_unused_volumes ;;
        6) cleanup_build_cache ;;
        7) 
            cleanup_dangling_images
            cleanup_app_backups
            cleanup_stopped_containers
            cleanup_unused_networks
            cleanup_unused_volumes
            cleanup_build_cache
            ;;
        0) 
            log_info "退出清理"
            exit 0
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 解析命令行参数
CLEANUP_ALL=false
CLEANUP_IMAGES=false
CLEANUP_CONTAINERS=false
CLEANUP_NETWORKS=false
CLEANUP_VOLUMES=false
CLEANUP_CACHE=false
KEEP_BACKUPS=3
DRY_RUN=false
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --all)
            CLEANUP_ALL=true
            shift
            ;;
        --images)
            CLEANUP_IMAGES=true
            shift
            ;;
        --containers)
            CLEANUP_CONTAINERS=true
            shift
            ;;
        --networks)
            CLEANUP_NETWORKS=true
            shift
            ;;
        --volumes)
            CLEANUP_VOLUMES=true
            shift
            ;;
        --cache)
            CLEANUP_CACHE=true
            shift
            ;;
        --keep-backups)
            KEEP_BACKUPS="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🧹 开始 Docker 清理"
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    # 显示清理前状态
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式：只显示将要清理的内容"
    fi
    
    show_disk_usage "🔍 清理前磁盘使用情况:"
    
    # 根据参数执行清理
    if [ "$CLEANUP_ALL" = true ]; then
        if [ "$FORCE" != true ] && [ "$DRY_RUN" != true ]; then
            read -p "确认清理所有 Docker 内容？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "取消清理"
                exit 0
            fi
        fi
        
        cleanup_dangling_images
        cleanup_app_backups
        cleanup_stopped_containers
        cleanup_unused_networks
        cleanup_unused_volumes
        cleanup_build_cache
        
    elif [ "$CLEANUP_IMAGES" = true ] || [ "$CLEANUP_CONTAINERS" = true ] || [ "$CLEANUP_NETWORKS" = true ] || [ "$CLEANUP_VOLUMES" = true ] || [ "$CLEANUP_CACHE" = true ]; then
        [ "$CLEANUP_IMAGES" = true ] && cleanup_dangling_images && cleanup_app_backups
        [ "$CLEANUP_CONTAINERS" = true ] && cleanup_stopped_containers
        [ "$CLEANUP_NETWORKS" = true ] && cleanup_unused_networks
        [ "$CLEANUP_VOLUMES" = true ] && cleanup_unused_volumes
        [ "$CLEANUP_CACHE" = true ] && cleanup_build_cache
    else
        # 交互式模式
        interactive_cleanup
    fi
    
    # 显示清理后状态
    if [ "$DRY_RUN" != true ]; then
        echo ""
        show_disk_usage "✅ 清理后磁盘使用情况:"
        log_success "🎉 Docker 清理完成！"
    else
        log_success "🔍 Dry Run 完成"
    fi
}

# 运行主函数
main
