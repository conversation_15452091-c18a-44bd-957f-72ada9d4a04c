# Kaia API 本地开发环境配置
NODE_ENV=development
PORT=3001

# 本地数据库配置 (连接到Docker MySQL)
DB_NAME=wolf_kaia
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=127.0.0.1
DB_PORT=3669

# 本地Redis配置 (连接到Docker Redis)
REDIS_HOST=127.0.0.1
REDIS_PORT=6257
REDIS_PASS=joetest1123

# API标识
API_INSTANCE=kaia-local
API_NAME="Wolf Fun Kaia API (Local Dev)"

# 其他配置
BOT_TOKEN="1"
JWT_SECRET_Master=BugkKneq6aoZmgoT6IfY23K9Kum5YQ
JWT_SECRET_Wallet=S9Ym3BlgGp1k10kWZUiOcTxE2nm5dw
TONCENTER_API_KEY=42a2067391ac48ffb6c3b96f6eb2a0209f6541caf5cbb368e9dd7ae6820dc4d2
TONCENTER_API_KEY_TEST=af8f3b969da812bef7aea12150729ac1e767f8a77cfa725589204c86bd27b95c
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=dqcoeatpsoysbjcj
EMAIL_FROM='"MooFun" <<EMAIL>>'
ROOM_CAPACITY=5
TELEGRAM_BOT_TOKEN="**********************************************"
WALLET_A_SEED="young female empty crazy raise outdoor surprise gospel flip observe patch observe nurse illegal erosion priority episode brain rail tongue sing galaxy live estate"
SKIP_PROOF_CHECK=true
TEST_ETH_PRIVATE_KEY=0x740fe33880d587c4f2a2f1c0190f10ebe0a74afd6073a08da8d78c3c8a7cd39d
DAPP_PORTAL_CLIENT_ID=6390fd82-d5da-462f-84e9-4755ad33c04e
DAPP_PORTAL_CLIENT_SECRET=5c3719ec-7d5c-4769-8370-e85c57b21082
BASE_URL=http://localhost:3001
KAIA_PRICE_UPDATE_SCHEDULE=0 */200 * * * *
KAIASCAN_API_KEY=bf3d0731-16e0-4fb9-9a54-c880cf6bc734
PHAROS_RPC_URL=https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47

PHRS_DEPOSIT_CONTRACT_ADDRESS=******************************************
PHAROS_RPC_URL=https://api.zan.top/node/v1/pharos/testnet/be532fc2eea94b26ae071d45bc1f7896
PHRS_HISTORICAL_START_BLOCK=0

# PHRS兑换USD汇率（默认：1 PHRS = 0.0001 USD）
PHRS_TO_USD_RATE=0.1

# 每10分钟执行一次
PHRS_PRICE_UPDATE_SCHEDULE=0 */10 * * * *
