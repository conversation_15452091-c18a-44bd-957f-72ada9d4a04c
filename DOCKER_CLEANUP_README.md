# Docker 镜像清理功能

## 概述

为了解决 Docker 镜像累积导致的磁盘空间问题，我们在 `update-code.sh` 脚本中集成了自动清理功能，并提供了独立的清理脚本 `cleanup-docker.sh`。

## 功能特性

### 🔄 自动清理（update-code.sh）
- 每次代码更新后自动清理不需要的 Docker 镜像
- 清理悬空镜像（dangling images）
- 清理旧版本的应用镜像（保留最近3个备份）
- 清理构建缓存
- 清理未使用的网络
- 显示清理前后的磁盘使用情况

### 🧹 手动清理（cleanup-docker.sh）
- 交互式清理选择
- 支持多种清理模式
- Dry-run 预览功能
- 可配置备份保留数量

## 使用方法

### 1. 自动清理（推荐）

在代码更新时自动执行清理：

```bash
# 正常更新（包含清理）
./update-code.sh

# 跳过清理
./update-code.sh --skip-cleanup

# 只更新 Kaia 服务（包含清理）
./update-code.sh kaia
```

### 2. 手动清理

#### 交互式清理（推荐新手）
```bash
./cleanup-docker.sh
```

#### 预览清理内容
```bash
# 预览所有清理内容
./cleanup-docker.sh --all --dry-run

# 预览镜像清理
./cleanup-docker.sh --images --dry-run
```

#### 强制清理所有内容
```bash
./cleanup-docker.sh --all --force
```

#### 分类清理
```bash
# 只清理镜像
./cleanup-docker.sh --images

# 只清理停止的容器
./cleanup-docker.sh --containers

# 只清理构建缓存
./cleanup-docker.sh --cache

# 只清理未使用的网络
./cleanup-docker.sh --networks

# 只清理未使用的卷
./cleanup-docker.sh --volumes
```

#### 自定义备份保留数量
```bash
# 保留最近5个备份镜像
./cleanup-docker.sh --images --keep-backups 5
```

## 清理内容详解

### 🗑️ 悬空镜像（Dangling Images）
- **定义**：没有标签的镜像，通常是构建过程中产生的中间镜像
- **安全性**：✅ 安全删除，不会影响运行中的容器
- **空间节省**：⭐⭐⭐

### 🏷️ 旧版本应用镜像
- **定义**：`moofun-kaia:backup_*` 和 `moofun-pharos:backup_*` 镜像
- **保留策略**：保留最近3个备份（可配置）
- **安全性**：✅ 安全删除，保留足够的回滚版本
- **空间节省**：⭐⭐⭐⭐

### 🔧 构建缓存
- **定义**：Docker 构建过程中的缓存层
- **安全性**：✅ 安全删除，只会影响下次构建速度
- **空间节省**：⭐⭐⭐⭐⭐

### 📦 停止的容器
- **定义**：状态为 `exited` 的容器
- **安全性**：✅ 安全删除，不影响运行中的服务
- **空间节省**：⭐⭐

### 🌐 未使用的网络
- **定义**：没有容器连接的自定义网络
- **安全性**：✅ 安全删除，不会影响默认网络
- **空间节省**：⭐

### 💾 未使用的卷
- **定义**：没有容器挂载的数据卷
- **安全性**：⚠️ 谨慎删除，可能包含重要数据
- **空间节省**：⭐⭐⭐

## 安全保障

### 🛡️ 保护机制
1. **运行容器保护**：不会删除正在运行的容器使用的镜像
2. **备份保留**：始终保留最近几个版本的备份镜像
3. **确认提示**：危险操作需要用户确认
4. **Dry-run 模式**：可以预览清理内容而不实际执行

### 🔒 不会删除的内容
- `moofun-kaia:latest` 和 `moofun-pharos:latest` 镜像
- 正在运行的容器
- 最近的备份镜像（默认保留3个）
- 系统默认网络（bridge、host、none）

## 监控和统计

### 📊 磁盘使用情况
清理前后都会显示详细的磁盘使用统计：

```bash
🐳 Docker 系统使用情况:
TYPE            TOTAL     ACTIVE    SIZE      RECLAIMABLE
Images          15        5         2.1GB     1.8GB (85%)
Containers      8         2         45MB      32MB (71%)
Local Volumes   3         1         156MB     98MB (63%)
Build Cache     12        0         1.2GB     1.2GB (100%)

📊 镜像统计:
  总镜像数: 15
  悬空镜像: 3
  Kaia 镜像: 6
  Pharos 镜像: 4
```

### 🎯 清理效果
- **平均节省空间**：每次清理可节省 500MB - 2GB
- **清理频率**：建议每次代码更新后自动清理
- **手动清理**：建议每周运行一次深度清理

## 故障排除

### ❓ 常见问题

**Q: 清理后构建变慢了？**
A: 这是正常现象，因为清理了构建缓存。首次构建会重新创建缓存。

**Q: 误删了重要镜像怎么办？**
A: 可以通过重新构建恢复，或者从备份镜像恢复。

**Q: 清理卡住了？**
A: 可能有容器正在使用镜像，先停止相关容器再清理。

### 🔧 手动恢复命令

```bash
# 查看所有镜像
docker images

# 查看所有容器
docker ps -a

# 重新构建镜像
./scripts/docker-build.sh kaia
./scripts/docker-build.sh pharos

# 从备份恢复
docker tag moofun-kaia:backup_20250728_143022 moofun-kaia:latest
```

## 测试功能

我们提供了测试脚本来验证清理功能：

```bash
# 运行清理功能测试
./test-cleanup.sh
```

测试脚本会：
1. 创建测试镜像和容器
2. 显示清理前状态
3. 运行清理脚本（dry-run 模式）
4. 自动清理测试资源

## 最佳实践

### ✅ 推荐做法
1. **定期清理**：每次代码更新时自动清理
2. **预览优先**：使用 `--dry-run` 预览清理内容
3. **保留备份**：保持默认的3个备份镜像
4. **监控空间**：定期检查 `docker system df`

### ❌ 避免做法
1. 不要删除 `latest` 标签的镜像
2. 不要在生产环境中清理卷数据
3. 不要在容器运行时强制删除镜像
4. 不要设置备份保留数量为0

## 配置选项

### update-code.sh 选项
- `--skip-cleanup`: 跳过自动清理
- 其他选项保持不变

### cleanup-docker.sh 选项
- `--all`: 清理所有内容
- `--images`: 只清理镜像
- `--containers`: 只清理容器
- `--networks`: 只清理网络
- `--volumes`: 只清理卷
- `--cache`: 只清理缓存
- `--keep-backups N`: 保留N个备份
- `--dry-run`: 预览模式
- `--force`: 强制清理

通过这些功能，您可以有效管理 Docker 镜像，防止磁盘空间无限增长，同时保持系统的稳定性和可回滚性。
