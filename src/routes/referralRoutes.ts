// src/routes/referralRoutes.ts
import { Router } from "express";
import {
  bindReferral,
  referrals,
  getReferralStatus
} from "../services/referralService";
import { MyRequest } from "../types/customRequest";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";
import * as jackpotChestController from "../controllers/jackpotChestController";
import { processOpenChest } from "../services/chestService";
import { Chest } from "../models";
import { sequelize } from "../config/db";
import { getDownlineList } from '../controllers/referralController';
import { NewTaskService } from '../services/NewTaskService';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义绑定推荐码请求体验证模式
const bindReferralSchema = {
  type: "object",
  properties: {
    code: { type: "string", minLength: 1 }
  },
  required: ["code"]
};

const validateBindReferral = ajv.compile(bindReferralSchema);


//@ts-ignore
router.post("/bind", walletAuthMiddleware, async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    // 验证请求体
    const valid = validateBindReferral(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateBindReferral.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const userId = myReq.user?.userId; // 从token解析到
    const walletId = myReq.user?.walletId; // 从token解析到
    const { code } = req.body;

   

    const result = await bindReferral(walletId!, userId!, code,transaction);
    await transaction.commit();

    // 异步更新推荐人的邀请好友任务进度
    const taskService = new NewTaskService();
    taskService.updateAllTaskProgress(result.referrerWalletId).catch(error => {
      console.error('更新邀请好友任务进度失败:', error);
    });

    res.json(successResponse(result, tFromRequest(req, "success.referralBound")));
  } catch (err: any) {
    await transaction.rollback();
    res.status(400).json(errorResponse(err.message));
  }
});

//@ts-ignore
router.get("/list", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    const userId = myReq.user?.userId; // 从token解析到
    if (!userId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserId")));
    }

    const result = await referrals(userId);
    res.json(successResponse(result, tFromRequest(req, "success.referralList")));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

//@ts-ignore
router.get("/status", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;

    if (!userId || !walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserOrWalletId")));
    }

    const result = await getReferralStatus(userId, walletId);

    res.json(successResponse(result, tFromRequest(req, "success.referralStatus")));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

// 推荐宝箱数量
router.get("/referral-chests/count", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.getReferralChestCount(req, res);
});

//@ts-ignore
router.post("/open", walletAuthMiddleware, async (req, res) => {

  const transaction = await sequelize.transaction();

  try {
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;

    if (!userId || !walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserOrWalletId")));
    }
    // 从数据库获取未开启的推荐宝箱数量
    const count = await Chest.count({
      where: {
        userId,
        walletId,
        type: 'referral',
        isOpened: false
      }
    });

    if (count === 0) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.noReferralChests")));
    }
    const result = await processOpenChest({
      userId,
      walletId: walletId!,
      count,
      chestType: 'referral', // 指定只打开推荐宝箱
      transaction
    });

    await transaction.commit();
    return res.json({
      ok: true,
      data: result
    });
  } catch (err: any) {
    await transaction.rollback();
    res.status(400).json(errorResponse(err.message));
  }
});


// 获取下线列表
router.get('/downline', walletAuthMiddleware, async (req, res) => {
  try {
    await getDownlineList(req, res);
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;
