import { ethers } from 'ethers';
import { PhrsDeposit } from '../models';

/**
 * PHRS监控调试工具
 * 用于排查监控服务无法获取记录的问题
 */
export class PhrsMonitorDebug {
  private provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;

  // 合约ABI
  private readonly contractABI = [
    "function getBalance() external view returns (uint256)",
    "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
    "function getUserBalance(address user) external view returns (uint256)",
    "function detectForcedDeposits() external view returns (uint256, bool)",
    "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS!;
    
    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);
    
    console.log(`🔍 PHRS监控调试工具初始化完成`);
    console.log(`📡 RPC URL: ${rpcUrl}`);
    console.log(`📋 合约地址: ${this.contractAddress}`);
  }

  /**
   * 运行完整的调试检查
   */
  async runFullDebug(): Promise<void> {
    console.log('\n🔍 开始PHRS监控调试检查...');
    console.log('=====================================');

    try {
      // 1. 检查网络连接
      await this.checkNetworkConnection();
      
      // 2. 检查合约状态
      await this.checkContractStatus();
      
      // 3. 检查数据库记录
      await this.checkDatabaseRecords();
      
      // 4. 检查最近的区块事件
      await this.checkRecentEvents();
      
      // 5. 手动查询特定区块范围的事件
      await this.manualEventQuery();
      
      // 6. 检查事件过滤器
      await this.testEventFilter();

      console.log('\n✅ 调试检查完成');
      console.log('=====================================');

    } catch (error) {
      console.error('❌ 调试过程中发生错误:', error);
    }
  }

  /**
   * 检查网络连接
   */
  private async checkNetworkConnection(): Promise<void> {
    console.log('\n📡 检查网络连接...');
    
    try {
      const network = await this.provider.getNetwork();
      const blockNumber = await this.provider.getBlockNumber();
      
      console.log(`✅ 网络连接正常`);
      console.log(`   网络名称: ${network.name}`);
      console.log(`   Chain ID: ${network.chainId}`);
      console.log(`   当前区块: ${blockNumber}`);
      
    } catch (error) {
      console.error('❌ 网络连接失败:', error);
      throw error;
    }
  }

  /**
   * 检查合约状态
   */
  private async checkContractStatus(): Promise<void> {
    console.log('\n📋 检查合约状态...');
    
    try {
      // 检查合约代码
      const code = await this.provider.getCode(this.contractAddress);
      if (code === '0x') {
        console.error('❌ 合约地址无代码，可能是错误的地址');
        return;
      }
      
      console.log('✅ 合约代码存在');
      
      // 获取合约信息
      const contractInfo = await this.contract.getContractInfo();
      console.log(`   最小充值金额: ${ethers.formatEther(contractInfo[0])} PHRS`);
      console.log(`   最大充值金额: ${ethers.formatEther(contractInfo[1])} PHRS`);
      console.log(`   合约总余额: ${ethers.formatEther(contractInfo[2])} PHRS`);
      console.log(`   合法充值总额: ${ethers.formatEther(contractInfo[3])} PHRS`);
      
      // 检查强制发送
      const [forcedAmount, hasForced] = await this.contract.detectForcedDeposits();
      if (hasForced) {
        console.log(`⚠️  检测到强制发送: ${ethers.formatEther(forcedAmount)} PHRS`);
      } else {
        console.log('✅ 未检测到强制发送');
      }
      
    } catch (error) {
      console.error('❌ 检查合约状态失败:', error);
    }
  }

  /**
   * 检查数据库记录
   */
  private async checkDatabaseRecords(): Promise<void> {
    console.log('\n💾 检查数据库记录...');
    
    try {
      const totalRecords = await PhrsDeposit.count();
      console.log(`📊 数据库中总记录数: ${totalRecords}`);
      
      if (totalRecords > 0) {
        // 获取最新的几条记录
        const recentRecords = await PhrsDeposit.findAll({
          order: [['createdAt', 'DESC']],
          limit: 5
        });
        
        console.log('📝 最近的充值记录:');
        recentRecords.forEach((record, index) => {
          console.log(`   ${index + 1}. 用户: ${record.userAddress}`);
          console.log(`      金额: ${ethers.formatEther(record.amount)} PHRS`);
          console.log(`      区块: ${record.blockNumber}`);
          console.log(`      时间: ${record.createdAt}`);
        });
        
        // 获取最后处理的区块号
        const lastRecord = await PhrsDeposit.findOne({
          order: [['blockNumber', 'DESC']],
          limit: 1
        });
        
        if (lastRecord) {
          console.log(`🔢 最后处理的区块号: ${lastRecord.blockNumber}`);
        }
      } else {
        console.log('⚠️  数据库中没有充值记录');
      }
      
    } catch (error) {
      console.error('❌ 检查数据库记录失败:', error);
    }
  }

  /**
   * 检查最近的区块事件
   */
  private async checkRecentEvents(): Promise<void> {
    console.log('\n🔍 检查最近区块的事件...');
    
    try {
      const currentBlock = await this.provider.getBlockNumber();
      const fromBlock = Math.max(0, currentBlock - 1000); // 检查最近1000个区块
      
      console.log(`📊 查询区块范围: ${fromBlock} 到 ${currentBlock}`);
      
      const filter = this.contract.filters.Deposit();
      const events = await this.contract.queryFilter(filter, fromBlock, currentBlock);
      
      console.log(`📡 找到 ${events.length} 个Deposit事件`);
      
      if (events.length > 0) {
        console.log('📝 事件详情:');
        events.slice(-5).forEach((event, index) => { // 显示最后5个事件
          if ('args' in event && event.args) {
            console.log(`   ${index + 1}. 区块: ${event.blockNumber}`);
            console.log(`      交易: ${event.transactionHash}`);
            console.log(`      用户: ${event.args[0]}`);
            console.log(`      金额: ${ethers.formatEther(event.args[1])} PHRS`);
            console.log(`      时间戳: ${new Date(Number(event.args[2]) * 1000).toLocaleString()}`);
          }
        });
      } else {
        console.log('⚠️  最近1000个区块中没有找到Deposit事件');
      }
      
    } catch (error) {
      console.error('❌ 检查最近事件失败:', error);
    }
  }

  /**
   * 手动查询特定区块范围的事件
   */
  private async manualEventQuery(): Promise<void> {
    console.log('\n🔧 手动查询事件...');
    
    try {
      // 如果有数据库记录，从最后一条记录开始查询
      const lastRecord = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      
      const currentBlock = await this.provider.getBlockNumber();
      let fromBlock = currentBlock - 100; // 默认查询最近100个区块
      
      if (lastRecord) {
        fromBlock = Number(lastRecord.blockNumber) + 1;
        console.log(`📊 从数据库最后记录的区块开始查询: ${fromBlock}`);
      } else {
        console.log(`📊 从最近100个区块开始查询: ${fromBlock}`);
      }
      
      if (fromBlock > currentBlock) {
        console.log('✅ 没有新区块需要处理');
        return;
      }
      
      console.log(`🔍 查询区块范围: ${fromBlock} 到 ${currentBlock}`);
      
      const filter = this.contract.filters.Deposit();
      const events = await this.contract.queryFilter(filter, fromBlock, currentBlock);
      
      console.log(`📡 找到 ${events.length} 个未处理的事件`);
      
      if (events.length > 0) {
        console.log('🚨 发现未处理的事件，这可能是监控服务的问题!');
        events.forEach((event, index) => {
          if ('args' in event && event.args) {
            console.log(`   ${index + 1}. 区块: ${event.blockNumber}, 交易: ${event.transactionHash}`);
          }
        });
      }
      
    } catch (error) {
      console.error('❌ 手动查询事件失败:', error);
    }
  }

  /**
   * 测试事件过滤器
   */
  private async testEventFilter(): Promise<void> {
    console.log('\n🧪 测试事件过滤器...');
    
    try {
      // 测试不同的过滤器配置
      const currentBlock = await this.provider.getBlockNumber();
      const testFromBlock = Math.max(0, currentBlock - 10);
      
      console.log(`🔍 测试区块范围: ${testFromBlock} 到 ${currentBlock}`);
      
      // 方法1: 使用合约过滤器
      const filter1 = this.contract.filters.Deposit();
      const events1 = await this.contract.queryFilter(filter1, testFromBlock, currentBlock);
      console.log(`📊 方法1 (合约过滤器): ${events1.length} 个事件`);
      
      // 方法2: 使用原始过滤器
      const depositTopic = ethers.id("Deposit(address,uint256,uint256)");
      const filter2 = {
        address: this.contractAddress,
        topics: [depositTopic],
        fromBlock: testFromBlock,
        toBlock: currentBlock
      };
      const logs = await this.provider.getLogs(filter2);
      console.log(`📊 方法2 (原始过滤器): ${logs.length} 个日志`);
      
      // 方法3: 解析日志
      const parsedEvents = logs.map(log => {
        try {
          return this.contract.interface.parseLog(log);
        } catch {
          return null;
        }
      }).filter(event => event !== null);
      
      console.log(`📊 方法3 (解析日志): ${parsedEvents.length} 个解析事件`);
      
    } catch (error) {
      console.error('❌ 测试事件过滤器失败:', error);
    }
  }
}

// 创建调试实例并导出
export const phrsMonitorDebug = new PhrsMonitorDebug();
