'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 更新 phrs_deposits 表的 transactionHash 字段长度
    await queryInterface.changeColumn('phrs_deposits', 'transactionHash', {
      type: Sequelize.STRING(70),
      allowNull: false,
      unique: true
    });
    
    console.log('✅ 已更新 phrs_deposits.transactionHash 字段长度为 70 个字符');
  },

  async down(queryInterface, Sequelize) {
    // 回滚：将字段长度改回 66
    await queryInterface.changeColumn('phrs_deposits', 'transactionHash', {
      type: Sequelize.STRING(66),
      allowNull: false,
      unique: true
    });
    
    console.log('✅ 已回滚 phrs_deposits.transactionHash 字段长度为 66 个字符');
  }
};
