'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查 pricePhrs 字段是否已存在
    const tableDescription = await queryInterface.describeTable('iap_products');

    if (!tableDescription.pricePhrs) {
      // 字段不存在，添加 pricePhrs 字段到 iap_products 表
      await queryInterface.addColumn('iap_products', 'pricePhrs', {
        type: Sequelize.DECIMAL(20, 4), // 支持大数值，4位小数
        allowNull: true,
        comment: 'PHRS价格，基于USD价格和PHRS汇率计算'
      });

      console.log('✅ 已添加 iap_products.pricePhrs 字段');
    } else {
      console.log('ℹ️ iap_products.pricePhrs 字段已存在，跳过添加操作');
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查 pricePhrs 字段是否存在
    const tableDescription = await queryInterface.describeTable('iap_products');

    if (tableDescription.pricePhrs) {
      // 字段存在，删除 pricePhrs 字段
      await queryInterface.removeColumn('iap_products', 'pricePhrs');

      console.log('✅ 已删除 iap_products.pricePhrs 字段');
    } else {
      console.log('ℹ️ iap_products.pricePhrs 字段不存在，跳过删除操作');
    }
  }
};
