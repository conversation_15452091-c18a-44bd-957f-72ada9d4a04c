'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 检查表是否已存在
      const tables = await queryInterface.showAllTables();

      if (!tables.includes('delivery_line_configs')) {
        // 创建配置表
        await queryInterface.createTable('delivery_line_configs', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
        },
        grade: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          unique: true,
          comment: '流水线等级',
        },
        profit: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '牛奶利润',
        },
        capacity: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '牛奶容量',
        },
        production_interval: {
          type: Sequelize.DECIMAL(3, 1),
          allowNull: false,
          comment: '生产间隔(秒)',
        },
        delivery_speed_display: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '显示的配送速度百分数',
        },
        upgrade_cost: {
          type: Sequelize.BIGINT.UNSIGNED,
          allowNull: false,
          comment: '升级花费',
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
      }, {
        transaction,
        comment: '流水线配置表'
      });

      // 添加索引
      try {
        await queryInterface.addIndex('delivery_line_configs', ['grade'], {
          unique: true,
          name: 'idx_delivery_line_configs_grade',
          transaction
        });
      } catch (error) {
        // 索引可能已存在，忽略错误
      }

      // 插入50级配置数据
      const configs = [
        {grade: 1, profit: 364, capacity: 364, production_interval: 2.0, delivery_speed_display: 100, upgrade_cost: 13096},
        {grade: 2, profit: 464, capacity: 464, production_interval: 2.0, delivery_speed_display: 100, upgrade_cost: 20043},
        {grade: 3, profit: 1048, capacity: 1048, production_interval: 1.9, delivery_speed_display: 110, upgrade_cost: 28583},
        {grade: 4, profit: 1198, capacity: 1198, production_interval: 1.9, delivery_speed_display: 110, upgrade_cost: 39214},
        {grade: 5, profit: 1899, capacity: 1899, production_interval: 1.8, delivery_speed_display: 120, upgrade_cost: 52496},
        {grade: 6, profit: 2083, capacity: 2083, production_interval: 1.8, delivery_speed_display: 120, upgrade_cost: 69100},
        {grade: 7, profit: 2841, capacity: 2841, production_interval: 1.7, delivery_speed_display: 130, upgrade_cost: 89837},
        {grade: 8, profit: 3050, capacity: 3050, production_interval: 1.7, delivery_speed_display: 130, upgrade_cost: 115699},
        {grade: 9, profit: 3822, capacity: 3822, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 147898},
        {grade: 10, profit: 4047, capacity: 4047, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 282663},
        {grade: 11, profit: 4264, capacity: 4264, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 372264},
        {grade: 12, profit: 4473, capacity: 4473, production_interval: 1.6, delivery_speed_display: 140, upgrade_cost: 488223},
        {grade: 13, profit: 4911, capacity: 4911, production_interval: 1.4, delivery_speed_display: 160, upgrade_cost: 638028},
        {grade: 14, profit: 5118, capacity: 5118, production_interval: 1.4, delivery_speed_display: 160, upgrade_cost: 831242},
        {grade: 15, profit: 5320, capacity: 5320, production_interval: 1.2, delivery_speed_display: 180, upgrade_cost: 1080077},
        {grade: 16, profit: 5517, capacity: 5517, production_interval: 1.2, delivery_speed_display: 180, upgrade_cost: 1400110},
        {grade: 17, profit: 5982, capacity: 5982, production_interval: 1.1, delivery_speed_display: 190, upgrade_cost: 1811199},
        {grade: 18, profit: 6179, capacity: 6179, production_interval: 1.1, delivery_speed_display: 190, upgrade_cost: 2338648},
        {grade: 19, profit: 6517, capacity: 6517, production_interval: 1.0, delivery_speed_display: 200, upgrade_cost: 3014677},
        {grade: 20, profit: 6711, capacity: 6711, production_interval: 1.0, delivery_speed_display: 200, upgrade_cost: 3880291},
        {grade: 21, profit: 6900, capacity: 6900, production_interval: 0.9, delivery_speed_display: 210, upgrade_cost: 4987655},
        {grade: 22, profit: 7542, capacity: 7542, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 8761173},
        {grade: 23, profit: 7770, capacity: 7770, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 11282638},
        {grade: 24, profit: 7995, capacity: 7995, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 14512118},
        {grade: 25, profit: 8218, capacity: 8218, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 18645076},
        {grade: 26, profit: 8438, capacity: 8438, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 23930263},
        {grade: 27, profit: 8655, capacity: 8655, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 30684106},
        {grade: 28, profit: 8871, capacity: 8871, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 39308951},
        {grade: 29, profit: 9992, capacity: 9992, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 50316190},
        {grade: 30, profit: 10224, capacity: 10224, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 64355560},
        {grade: 31, profit: 11404, capacity: 11404, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 82252268},
        {grade: 32, profit: 11653, capacity: 11653, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 105054019},
        {grade: 33, profit: 12890, capacity: 12890, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 134090549},
        {grade: 34, profit: 13154, capacity: 13154, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 171049012},
        {grade: 35, profit: 13416, capacity: 13416, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 218069387},
        {grade: 36, profit: 13676, capacity: 13676, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 277865208},
        {grade: 37, profit: 15006, capacity: 15006, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 353876314},
        {grade: 38, profit: 15281, capacity: 15281, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 450462057},
        {grade: 39, profit: 16665, capacity: 16665, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 573145646},
        {grade: 40, profit: 16956, capacity: 16956, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 728923105},
        {grade: 41, profit: 18394, capacity: 18394, production_interval: 0.7, delivery_speed_display: 230, upgrade_cost: 926653847},
        {grade: 42, profit: 19456, capacity: 19456, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 1715251498},
        {grade: 43, profit: 21055, capacity: 21055, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 2183862028},
        {grade: 44, profit: 21437, capacity: 21437, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 2779348972},
        {grade: 45, profit: 23101, capacity: 23101, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 3535813480},
        {grade: 46, profit: 23502, capacity: 23502, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 4496466697},
        {grade: 47, profit: 25229, capacity: 25229, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 8312680849},
        {grade: 48, profit: 25648, capacity: 25648, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 10648183485},
        {grade: 49, profit: 27438, capacity: 27438, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 13635316108},
        {grade: 50, profit: 27876, capacity: 27876, production_interval: 0.5, delivery_speed_display: 250, upgrade_cost: 17454840843}
      ];

      // 添加时间戳
      const now = new Date();
      const configsWithTimestamps = configs.map(config => ({
        ...config,
        created_at: now,
        updated_at: now
      }));

        await queryInterface.bulkInsert('delivery_line_configs', configsWithTimestamps, { transaction });
      } else {
        console.log('✅ 流水线配置表已存在，跳过创建');
      }

      await transaction.commit();
      console.log('✅ 流水线配置表迁移完成');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 创建流水线配置表失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('delivery_line_configs');
  }
};
