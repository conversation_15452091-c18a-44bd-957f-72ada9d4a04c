'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始添加PHRS余额相关字段到user_wallets表...');

      // 检查字段是否已存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      // 添加PHRS余额字段（如果不存在）
      if (!tableDescription.phrsBalance) {
        console.log('添加 phrsBalance 字段...');
        await queryInterface.addColumn('user_wallets', 'phrsBalance', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
          comment: 'PHRS代币余额'
        }, { transaction });
        console.log('✅ phrsBalance 字段添加成功');
      } else {
        console.log('⚠️  phrsBalance 字段已存在，跳过');
      }

      // 添加PHRS钱包地址字段（用于关联区块链地址）
      if (!tableDescription.phrsWalletAddress) {
        console.log('添加 phrsWalletAddress 字段...');
        await queryInterface.addColumn('user_wallets', 'phrsWalletAddress', {
          type: Sequelize.STRING(42),
          allowNull: true,
          comment: 'PHRS充值关联的钱包地址'
        }, { transaction });
        console.log('✅ phrsWalletAddress 字段添加成功');
      } else {
        console.log('⚠️  phrsWalletAddress 字段已存在，跳过');
      }

      // 添加最后PHRS更新时间字段
      if (!tableDescription.lastPhrsUpdateTime) {
        console.log('添加 lastPhrsUpdateTime 字段...');
        await queryInterface.addColumn('user_wallets', 'lastPhrsUpdateTime', {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '最后PHRS余额更新时间'
        }, { transaction });
        console.log('✅ lastPhrsUpdateTime 字段添加成功');
      } else {
        console.log('⚠️  lastPhrsUpdateTime 字段已存在，跳过');
      }

      // 检查并添加索引
      console.log('检查并添加索引...');
      const indexes = await queryInterface.showIndex('user_wallets');
      const indexNames = indexes.map(index => index.name);

      // 添加PHRS余额索引
      if (!indexNames.includes('idx_user_wallets_phrs_balance')) {
        console.log('添加 phrsBalance 索引...');
        await queryInterface.addIndex('user_wallets', ['phrsBalance'], {
          name: 'idx_user_wallets_phrs_balance',
          transaction
        });
        console.log('✅ phrsBalance 索引添加成功');
      } else {
        console.log('⚠️  phrsBalance 索引已存在，跳过');
      }

      // 添加PHRS钱包地址索引
      if (!indexNames.includes('idx_user_wallets_phrs_wallet_address')) {
        console.log('添加 phrsWalletAddress 索引...');
        await queryInterface.addIndex('user_wallets', ['phrsWalletAddress'], {
          name: 'idx_user_wallets_phrs_wallet_address',
          transaction
        });
        console.log('✅ phrsWalletAddress 索引添加成功');
      } else {
        console.log('⚠️  phrsWalletAddress 索引已存在，跳过');
      }

      // 添加最后更新时间索引
      if (!indexNames.includes('idx_user_wallets_last_phrs_update')) {
        console.log('添加 lastPhrsUpdateTime 索引...');
        await queryInterface.addIndex('user_wallets', ['lastPhrsUpdateTime'], {
          name: 'idx_user_wallets_last_phrs_update',
          transaction
        });
        console.log('✅ lastPhrsUpdateTime 索引添加成功');
      } else {
        console.log('⚠️  lastPhrsUpdateTime 索引已存在，跳过');
      }

      await transaction.commit();
      console.log('🎉 PHRS余额字段迁移完成！');
      console.log('📊 添加的字段：');
      console.log('   - phrsBalance (DECIMAL(65,3)) - PHRS代币余额');
      console.log('   - phrsWalletAddress (STRING(42)) - PHRS充值关联的钱包地址');
      console.log('   - lastPhrsUpdateTime (DATE) - 最后PHRS余额更新时间');
      console.log('📈 添加的索引：');
      console.log('   - idx_user_wallets_phrs_balance');
      console.log('   - idx_user_wallets_phrs_wallet_address');
      console.log('   - idx_user_wallets_last_phrs_update');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ PHRS余额字段迁移失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始回滚PHRS余额相关字段...');

      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      // 删除索引
      console.log('删除索引...');
      const indexes = await queryInterface.showIndex('user_wallets');
      const indexNames = indexes.map(index => index.name);

      if (indexNames.includes('idx_user_wallets_phrs_balance')) {
        console.log('删除 phrsBalance 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_phrs_balance', { transaction });
      }

      if (indexNames.includes('idx_user_wallets_phrs_wallet_address')) {
        console.log('删除 phrsWalletAddress 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_phrs_wallet_address', { transaction });
      }

      if (indexNames.includes('idx_user_wallets_last_phrs_update')) {
        console.log('删除 lastPhrsUpdateTime 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_last_phrs_update', { transaction });
      }

      // 删除字段
      if (tableDescription.phrsBalance) {
        console.log('删除 phrsBalance 字段...');
        await queryInterface.removeColumn('user_wallets', 'phrsBalance', { transaction });
      }

      if (tableDescription.phrsWalletAddress) {
        console.log('删除 phrsWalletAddress 字段...');
        await queryInterface.removeColumn('user_wallets', 'phrsWalletAddress', { transaction });
      }

      if (tableDescription.lastPhrsUpdateTime) {
        console.log('删除 lastPhrsUpdateTime 字段...');
        await queryInterface.removeColumn('user_wallets', 'lastPhrsUpdateTime', { transaction });
      }

      await transaction.commit();
      console.log('🔄 PHRS余额字段回滚完成！');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ PHRS余额字段回滚失败:', error);
      throw error;
    }
  }
};
