'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查表是否已存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('delivery_lines')) {
        await queryInterface.createTable('delivery_lines', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
        },
        walletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'user_wallets',
            key: 'id',
          },
        },
        level: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          defaultValue: 1,
        },
        deliverySpeed: {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 1, // 1秒/次
        },
        blockUnit: {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 5, // 5牛奶/方块
        },
        blockPrice: {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 5, // 5 GEM/方块
        },
        upgradeCost: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          defaultValue: 500, // 初始升级费用
        },
        lastDeliveryTime: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('NOW'),
        },
        pendingMilk: {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 0,
        },
        pendingBlocks: {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 0,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('NOW'),
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('NOW'),
        },
      });
        console.log('成功创建delivery_lines表');
      } else {
        console.log('delivery_lines表已存在，跳过创建');
      }
    } catch (error) {
      console.error('迁移错误:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (tables.includes('delivery_lines')) {
        await queryInterface.dropTable('delivery_lines');
        console.log('成功删除delivery_lines表');
      } else {
        console.log('delivery_lines表不存在，跳过删除');
      }
    } catch (error) {
      console.error('迁移回滚错误:', error);
      throw error;
    }
  },
};