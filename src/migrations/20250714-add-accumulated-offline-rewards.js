'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 检查字段是否已存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      // 添加累积的离线宝石奖励字段（如果不存在）
      if (!tableDescription.accumulatedOfflineGems) {
        console.log('添加 accumulatedOfflineGems 字段...');
        await queryInterface.addColumn('user_wallets', 'accumulatedOfflineGems', {
          type: Sequelize.DECIMAL(65, 3),
          allowNull: false,
          defaultValue: 0,
          comment: '累积的离线宝石奖励'
        }, { transaction });
      } else {
        console.log('accumulatedOfflineGems 字段已存在，跳过');
      }

      // 添加上次离线奖励计算时间字段（如果不存在）
      if (!tableDescription.lastOfflineRewardCalculation) {
        console.log('添加 lastOfflineRewardCalculation 字段...');
        await queryInterface.addColumn('user_wallets', 'lastOfflineRewardCalculation', {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '上次离线奖励计算时间'
        }, { transaction });
      } else {
        console.log('lastOfflineRewardCalculation 字段已存在，跳过');
      }

      // 检查并添加索引
      const indexes = await queryInterface.showIndex('user_wallets');
      const indexNames = indexes.map(index => index.name);

      if (!indexNames.includes('idx_user_wallets_accumulated_offline_gems')) {
        console.log('添加 accumulatedOfflineGems 索引...');
        await queryInterface.addIndex('user_wallets', ['accumulatedOfflineGems'], {
          name: 'idx_user_wallets_accumulated_offline_gems',
          transaction
        });
      } else {
        console.log('accumulatedOfflineGems 索引已存在，跳过');
      }

      if (!indexNames.includes('idx_user_wallets_last_offline_calculation')) {
        console.log('添加 lastOfflineRewardCalculation 索引...');
        await queryInterface.addIndex('user_wallets', ['lastOfflineRewardCalculation'], {
          name: 'idx_user_wallets_last_offline_calculation',
          transaction
        });
      } else {
        console.log('lastOfflineRewardCalculation 索引已存在，跳过');
      }

      await transaction.commit();
      console.log('✅ 迁移完成');

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 检查并删除索引
      const indexes = await queryInterface.showIndex('user_wallets');
      const indexNames = indexes.map(index => index.name);

      if (indexNames.includes('idx_user_wallets_last_offline_calculation')) {
        console.log('删除 lastOfflineRewardCalculation 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_last_offline_calculation', { transaction });
      }

      if (indexNames.includes('idx_user_wallets_accumulated_offline_gems')) {
        console.log('删除 accumulatedOfflineGems 索引...');
        await queryInterface.removeIndex('user_wallets', 'idx_user_wallets_accumulated_offline_gems', { transaction });
      }

      // 检查并删除字段
      const tableDescription = await queryInterface.describeTable('user_wallets');

      if (tableDescription.lastOfflineRewardCalculation) {
        console.log('删除 lastOfflineRewardCalculation 字段...');
        await queryInterface.removeColumn('user_wallets', 'lastOfflineRewardCalculation', { transaction });
      }

      if (tableDescription.accumulatedOfflineGems) {
        console.log('删除 accumulatedOfflineGems 字段...');
        await queryInterface.removeColumn('user_wallets', 'accumulatedOfflineGems', { transaction });
      }

      await transaction.commit();
      console.log('✅ 回滚完成');

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
