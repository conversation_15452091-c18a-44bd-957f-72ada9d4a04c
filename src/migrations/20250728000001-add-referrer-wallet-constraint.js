'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始添加 referrerWalletId 外键约束...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('user_wallets')) {
        console.log('⚠️  user_wallets 表不存在，跳过迁移');
        await transaction.commit();
        return;
      }

      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');
      if (!tableDescription.referrerWalletId) {
        console.log('⚠️  referrerWalletId 字段不存在，跳过迁移');
        await transaction.commit();
        return;
      }

      // 检查是否已存在外键约束
      const [existingConstraints] = await queryInterface.sequelize.query(`
        SELECT CONSTRAINT_NAME 
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'user_wallets' 
          AND COLUMN_NAME = 'referrerWalletId'
          AND REFERENCED_TABLE_NAME IS NOT NULL
      `, { transaction });

      if (existingConstraints.length > 0) {
        console.log(`✅ referrerWalletId 外键约束已存在: ${existingConstraints[0].CONSTRAINT_NAME}`);
        await transaction.commit();
        return;
      }

      // 清理无效的referrerWalletId引用
      console.log('🧹 清理无效的 referrerWalletId 引用...');
      const [cleanupResult] = await queryInterface.sequelize.query(`
        UPDATE user_wallets 
        SET referrerWalletId = NULL 
        WHERE referrerWalletId IS NOT NULL 
          AND referrerWalletId NOT IN (SELECT id FROM (SELECT id FROM user_wallets) AS temp)
      `, { transaction });

      if (cleanupResult.affectedRows > 0) {
        console.log(`🧹 清理了 ${cleanupResult.affectedRows} 条无效引用`);
      }

      // 添加外键约束
      console.log('🔗 添加 referrerWalletId 外键约束...');
      await queryInterface.addConstraint('user_wallets', {
        fields: ['referrerWalletId'],
        type: 'foreign key',
        name: 'user_wallets_referrer_fk',
        references: {
          table: 'user_wallets',
          field: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        transaction
      });

      console.log('✅ referrerWalletId 外键约束添加成功');

      await transaction.commit();
      console.log('🎉 迁移完成！');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始回滚 referrerWalletId 外键约束...');

      // 检查约束是否存在
      const [existingConstraints] = await queryInterface.sequelize.query(`
        SELECT CONSTRAINT_NAME 
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'user_wallets' 
          AND COLUMN_NAME = 'referrerWalletId'
          AND REFERENCED_TABLE_NAME IS NOT NULL
      `, { transaction });

      if (existingConstraints.length > 0) {
        const constraintName = existingConstraints[0].CONSTRAINT_NAME;
        console.log(`🗑️  移除外键约束: ${constraintName}`);
        
        await queryInterface.removeConstraint('user_wallets', constraintName, { transaction });
        console.log('✅ 外键约束移除成功');
      } else {
        console.log('⚠️  未找到需要移除的外键约束');
      }

      await transaction.commit();
      console.log('✅ 回滚完成');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 回滚失败:', error);
      throw error;
    }
  }
};
