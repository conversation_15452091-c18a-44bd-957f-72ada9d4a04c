'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始添加 ticket_fragment 字段到 user_wallets 表...');

      // 检查字段是否已存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      // 添加 ticket_fragment 字段（如果不存在）
      if (!tableDescription.ticket_fragment) {
        console.log('添加 ticket_fragment 字段...');
        await queryInterface.addColumn('user_wallets', 'ticket_fragment', {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
          comment: '门票碎片数量'
        }, { transaction });
        console.log('✅ ticket_fragment 字段添加成功');
      } else {
        console.log('⚠️  ticket_fragment 字段已存在，跳过');
      }

      await transaction.commit();
      console.log('✅ 迁移完成：ticket_fragment 字段已成功添加到 user_wallets 表');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 迁移失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始回滚 ticket_fragment 字段...');

      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');

      if (tableDescription.ticket_fragment) {
        console.log('移除 ticket_fragment 字段...');
        await queryInterface.removeColumn('user_wallets', 'ticket_fragment', { transaction });
        console.log('✅ ticket_fragment 字段移除成功');
      } else {
        console.log('⚠️  ticket_fragment 字段不存在，跳过移除');
      }

      await transaction.commit();
      console.log('✅ 回滚完成：ticket_fragment 字段已从 user_wallets 表中移除');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ 回滚失败:', error);
      throw error;
    }
  }
};
