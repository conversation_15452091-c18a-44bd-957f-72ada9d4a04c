'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查shareCodeId列是否已存在
    const columns = await queryInterface.describeTable('chest_boosts');
    if (!columns.shareCodeId) {
      await queryInterface.addColumn('chest_boosts', 'shareCodeId', {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        references: {
          model: 'share_boost_links',
          key: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
      });
    }

    // 检查索引是否已存在，不存在才添加
    try {
      const indexes = await queryInterface.showIndex('chest_boosts');
      const indexExists = indexes.some(index =>
        index.name === 'chest_boosts_share_code_id' ||
        (index.fields && index.fields.some(field => field.attribute === 'shareCodeId'))
      );

      if (!indexExists) {
        await queryInterface.addIndex('chest_boosts', ['shareCodeId']);
      }
    } catch (error) {
      // 如果索引已存在，忽略错误
      if (!error.message.includes('Duplicate key name')) {
        throw error;
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 检查shareCodeId列是否存在
    const columns = await queryInterface.describeTable('chest_boosts');
    if (columns.shareCodeId) {
      // 先删除索引（如果存在）
      try {
        const indexes = await queryInterface.showIndex('chest_boosts');
        const indexExists = indexes.some(index =>
          index.name === 'chest_boosts_share_code_id' ||
          (index.fields && index.fields.some(field => field.attribute === 'shareCodeId'))
        );

        if (indexExists) {
          await queryInterface.removeIndex('chest_boosts', ['shareCodeId']);
        }
      } catch (error) {
        // 如果索引不存在，忽略错误
        console.log('索引可能已经不存在:', error.message);
      }

      // 再删除列
      await queryInterface.removeColumn('chest_boosts', 'shareCodeId');
    }
  }
};