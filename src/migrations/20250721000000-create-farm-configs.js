'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    
    // 创建 farm_configs 表
    if (!tables.includes('farm_configs')) {
      await queryInterface.createTable('farm_configs', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
        },
        grade: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '等级 (0-50)',
        },
        production: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '每秒产出计算用值',
        },
        cow: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '奶牛数量',
        },
        speed: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '生产速度百分比',
        },
        milk: {
          type: Sequelize.DECIMAL(15, 3),
          allowNull: false,
          comment: '牛奶生产',
        },
        cost: {
          type: Sequelize.BIGINT.UNSIGNED,
          allowNull: false,
          comment: '升级花费',
        },
        offline: {
          type: Sequelize.DECIMAL(15, 3),
          allowNull: false,
          comment: '离线产出',
        },
        version: {
          type: Sequelize.STRING(50),
          allowNull: false,
          defaultValue: 'default',
          comment: '配置版本',
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: '是否激活',
        },
        createdBy: {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: '创建者',
        },
        remark: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '版本说明',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
      });

      // 添加索引
      await queryInterface.addIndex('farm_configs', ['grade'], {
        name: 'idx_farm_configs_grade',
      });

      await queryInterface.addIndex('farm_configs', ['version'], {
        name: 'idx_farm_configs_version',
      });

      await queryInterface.addIndex('farm_configs', ['isActive'], {
        name: 'idx_farm_configs_active',
      });

      await queryInterface.addIndex('farm_configs', ['createdAt'], {
        name: 'idx_farm_configs_created_at',
      });

      // 添加唯一索引
      await queryInterface.addIndex('farm_configs', ['grade', 'version'], {
        unique: true,
        name: 'unique_farm_configs_grade_version',
      });

      console.log('farm_configs 表创建成功');
    }

    // 创建 farm_config_versions 表
    if (!tables.includes('farm_config_versions')) {
      await queryInterface.createTable('farm_config_versions', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
        },
        version: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true,
          comment: '版本号',
        },
        name: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: '版本名称',
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '版本描述',
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: '是否激活',
        },
        configCount: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          defaultValue: 0,
          comment: '配置条数',
        },
        createdBy: {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: '创建者',
        },
        activatedAt: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '激活时间',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
        },
      });

      // 添加索引
      await queryInterface.addIndex('farm_config_versions', ['isActive'], {
        name: 'idx_farm_config_versions_active',
      });

      await queryInterface.addIndex('farm_config_versions', ['createdAt'], {
        name: 'idx_farm_config_versions_created_at',
      });

      // 添加唯一索引
      await queryInterface.addIndex('farm_config_versions', ['version'], {
        unique: true,
        name: 'unique_farm_config_versions_version',
      });

      console.log('farm_config_versions 表创建成功');
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 删除表（按依赖关系逆序删除）
    await queryInterface.dropTable('farm_configs');
    await queryInterface.dropTable('farm_config_versions');
    console.log('farm_configs 和 farm_config_versions 表已删除');
  },
};
