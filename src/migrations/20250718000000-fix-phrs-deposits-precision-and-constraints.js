'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始修复PHRS充值记录表...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      
      if (!tables.includes('phrs_deposits')) {
        console.log('⚠️  phrs_deposits 表不存在，跳过修复');
        await transaction.commit();
        return;
      }

      // 1. 修复 walletId 字段，允许 null（用于未注册用户）
      console.log('1. 修复 walletId 字段约束，允许 null...');
      await queryInterface.changeColumn('phrs_deposits', 'walletId', {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true, // 修复：允许 null
        references: {
          model: 'user_wallets',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL' // 修复：当用户删除时设置为 null
      }, { transaction });

      // 2. 修复 amount 字段精度，从 DECIMAL(65, 3) 改为 DECIMAL(65, 18)
      console.log('2. 修复 amount 字段精度，从 3 位小数改为 18 位小数...');
      await queryInterface.changeColumn('phrs_deposits', 'amount', {
        type: Sequelize.DECIMAL(65, 18), // 修复：使用 18 位小数精度
        allowNull: false,
        comment: '充值金额（18位精度）'
      }, { transaction });

      // 3. 修复 transactionHash 字段长度
      console.log('3. 修复 transactionHash 字段长度...');
      await queryInterface.changeColumn('phrs_deposits', 'transactionHash', {
        type: Sequelize.STRING(70), // 修复：增加长度以确保完整存储
        allowNull: false,
        unique: true,
        comment: '区块链交易哈希'
      }, { transaction });

      // 4. 检查并添加缺失的索引
      console.log('4. 检查并添加缺失的索引...');
      
      const existingIndexes = await queryInterface.showIndex('phrs_deposits');
      const existingIndexNames = existingIndexes.map(index => index.name);
      
      const requiredIndexes = [
        {
          name: 'idx_phrs_deposits_wallet_id_null',
          fields: ['walletId'],
          where: { walletId: { [Sequelize.Op.not]: null } }
        },
        {
          name: 'idx_phrs_deposits_user_address_status',
          fields: ['userAddress', 'status']
        },
        {
          name: 'idx_phrs_deposits_block_number_status',
          fields: ['blockNumber', 'status']
        }
      ];

      for (const index of requiredIndexes) {
        if (!existingIndexNames.includes(index.name)) {
          console.log(`添加索引: ${index.name}`);
          await queryInterface.addIndex('phrs_deposits', index.fields, {
            name: index.name,
            where: index.where,
            transaction
          });
        } else {
          console.log(`⚠️  索引 ${index.name} 已存在，跳过`);
        }
      }

      // 5. 验证数据完整性
      console.log('5. 验证数据完整性...');
      
      // 检查是否有无效的充值记录
      const [invalidRecords] = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM phrs_deposits WHERE walletId IS NULL AND status = 'CONFIRMED'`,
        { transaction }
      );
      
      if (invalidRecords[0].count > 0) {
        console.log(`⚠️  发现 ${invalidRecords[0].count} 条无效记录（walletId 为 null 但状态为 CONFIRMED）`);
        
        // 将这些记录状态改为 FAILED
        await queryInterface.sequelize.query(
          `UPDATE phrs_deposits SET status = 'FAILED', errorMessage = 'Invalid record: walletId is null' WHERE walletId IS NULL AND status = 'CONFIRMED'`,
          { transaction }
        );
        
        console.log('✅ 已修复无效记录状态');
      }

      await transaction.commit();
      console.log('🎉 PHRS充值记录表修复完成！');
      console.log('📊 修复内容：');
      console.log('   - walletId 字段：允许 null（用于未注册用户）');
      console.log('   - amount 字段：精度从 3 位小数改为 18 位小数');
      console.log('   - transactionHash 字段：长度增加到 70 字符');
      console.log('   - 添加了复合索引以提高查询性能');
      console.log('   - 验证并修复了数据完整性');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ PHRS充值记录表修复失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始回滚PHRS充值记录表修复...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      
      if (!tables.includes('phrs_deposits')) {
        console.log('⚠️  phrs_deposits 表不存在，跳过回滚');
        await transaction.commit();
        return;
      }

      // 1. 回滚 walletId 字段约束
      console.log('1. 回滚 walletId 字段约束...');
      await queryInterface.changeColumn('phrs_deposits', 'walletId', {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false, // 回滚：不允许 null
        references: {
          model: 'user_wallets',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }, { transaction });

      // 2. 回滚 amount 字段精度
      console.log('2. 回滚 amount 字段精度...');
      await queryInterface.changeColumn('phrs_deposits', 'amount', {
        type: Sequelize.DECIMAL(65, 3), // 回滚：使用 3 位小数精度
        allowNull: false,
        comment: '充值金额'
      }, { transaction });

      // 3. 回滚 transactionHash 字段长度
      console.log('3. 回滚 transactionHash 字段长度...');
      await queryInterface.changeColumn('phrs_deposits', 'transactionHash', {
        type: Sequelize.STRING(66), // 回滚：原始长度
        allowNull: false,
        unique: true,
        comment: '区块链交易哈希'
      }, { transaction });

      // 4. 删除新添加的索引
      console.log('4. 删除新添加的索引...');
      
      const indexesToRemove = [
        'idx_phrs_deposits_wallet_id_null',
        'idx_phrs_deposits_user_address_status',
        'idx_phrs_deposits_block_number_status'
      ];

      const existingIndexes = await queryInterface.showIndex('phrs_deposits');
      const existingIndexNames = existingIndexes.map(index => index.name);

      for (const indexName of indexesToRemove) {
        if (existingIndexNames.includes(indexName)) {
          console.log(`删除索引: ${indexName}`);
          await queryInterface.removeIndex('phrs_deposits', indexName, { transaction });
        }
      }

      await transaction.commit();
      console.log('🔄 PHRS充值记录表修复回滚完成！');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ PHRS充值记录表修复回滚失败:', error);
      throw error;
    }
  }
};
