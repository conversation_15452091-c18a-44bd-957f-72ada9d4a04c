#!/usr/bin/env ts-node

import '../config/env'; // 导入统一的环境配置管理
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量

async function main() {
  console.log('🔍 PHRS监控服务诊断');
  console.log('===================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 1. 检查环境变量
    console.log('\n📋 环境变量检查:');
    const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    const rpcUrl = process.env.PHAROS_RPC_URL;
    
    console.log(`   PHRS_DEPOSIT_CONTRACT_ADDRESS: ${contractAddress || '❌ 未设置'}`);
    console.log(`   PHAROS_RPC_URL: ${rpcUrl || '❌ 未设置'}`);

    if (!contractAddress) {
      console.log('❌ 合约地址未设置，监控服务无法启动');
      return;
    }

    // 2. 检查服务状态
    console.log('\n📊 服务状态检查:');
    const status = phrsDepositService.getStatus();
    console.log(`   监听状态: ${status.isListening ? '✅ 运行中' : '❌ 已停止'}`);
    console.log(`   合约地址: ${status.contractAddress}`);
    console.log(`   最后处理区块: ${status.lastProcessedBlock}`);
    console.log(`   RPC地址: ${status.providerUrl}`);

    // 3. 如果服务未运行，尝试启动
    if (!status.isListening) {
      console.log('\n🔄 尝试启动监控服务...');
      try {
        await phrsDepositService.startListening();
        console.log('✅ 监控服务启动成功');
        
        // 等待几秒钟观察日志
        console.log('\n⏳ 等待10秒观察监控服务运行状态...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
      } catch (error) {
        console.error('❌ 启动监控服务失败:', error);
      }
    } else {
      console.log('\n✅ 监控服务已在运行');
      
      // 等待几秒钟观察日志
      console.log('\n⏳ 等待10秒观察监控服务运行状态...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    }

    // 4. 再次检查状态
    console.log('\n📊 最终状态检查:');
    const finalStatus = phrsDepositService.getStatus();
    console.log(`   监听状态: ${finalStatus.isListening ? '✅ 运行中' : '❌ 已停止'}`);
    console.log(`   最后处理区块: ${finalStatus.lastProcessedBlock}`);

    // 5. 检查最近的日志输出
    console.log('\n💡 诊断建议:');
    if (!finalStatus.isListening) {
      console.log('❌ 监控服务未运行，可能的原因:');
      console.log('   1. 环境变量配置错误');
      console.log('   2. 网络连接问题');
      console.log('   3. 合约地址无效');
      console.log('   4. RPC节点不可用');
    } else {
      console.log('✅ 监控服务正常运行');
      console.log('   - 每10秒检查一次新区块');
      console.log('   - 查看控制台日志确认轮询执行');
      console.log('   - 如果没有日志，可能是没有新区块或新事件');
    }

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
  }

  // 不要退出，让监控服务继续运行
  console.log('\n🔄 监控服务将继续运行，按 Ctrl+C 退出...');
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});