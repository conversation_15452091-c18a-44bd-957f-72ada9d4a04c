// src/scripts/testPhrsDepositService.ts
import { phrsDepositService } from '../services/phrsDepositService';
import { sequelize } from '../config/db';
import { PhrsDeposit, UserWallet } from '../models';
import { Op } from 'sequelize';

/**
 * 测试PHRS充值服务的事务处理修复
 */
async function testPhrsDepositService() {
  console.log('🧪 开始测试PHRS充值服务事务处理修复...');
  console.log('================================================');

  try {
    // 1. 检查数据库连接
    console.log('1. 检查数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接正常');

    // 2. 检查服务状态
    console.log('\n2. 检查服务状态...');
    const status = phrsDepositService.getStatus();
    console.log('服务状态:', status);

    // 3. 进行健康检查
    console.log('\n3. 进行健康检查...');
    const healthCheck = await phrsDepositService.healthCheck();
    console.log('健康检查结果:', healthCheck);

    // 4. 检查现有的充值记录
    console.log('\n4. 检查现有的充值记录...');
    const existingDeposits = await PhrsDeposit.findAll({
      limit: 5,
      order: [['createdAt', 'DESC']]
    });
    console.log(`找到 ${existingDeposits.length} 条最近的充值记录`);
    
    existingDeposits.forEach((deposit, index) => {
      console.log(`   记录 ${index + 1}:`);
      console.log(`     交易哈希: ${deposit.transactionHash}`);
      console.log(`     状态: ${deposit.status}`);
      console.log(`     金额: ${deposit.amount}`);
      console.log(`     用户地址: ${deposit.userAddress}`);
      console.log(`     钱包ID: ${deposit.walletId || 'null'}`);
      console.log(`     错误信息: ${deposit.errorMessage || 'none'}`);
    });

    // 5. 检查用户钱包
    console.log('\n5. 检查用户钱包...');
    const userWallets = await UserWallet.findAll({
      where: {
        phrsWalletAddress: { [Op.ne]: null as any }
      },
      limit: 3
    });
    console.log(`找到 ${userWallets.length} 个有PHRS地址的用户钱包`);
    
    userWallets.forEach((wallet, index) => {
      console.log(`   钱包 ${index + 1}:`);
      console.log(`     ID: ${wallet.id}`);
      console.log(`     PHRS地址: ${wallet.phrsWalletAddress}`);
      console.log(`     PHRS余额: ${wallet.phrsBalance || '0'}`);
      console.log(`     最后更新: ${wallet.lastPhrsUpdateTime || 'never'}`);
    });

    // 6. 测试处理特定区块（如果有的话）
    console.log('\n6. 测试处理最近区块...');
    try {
      const currentBlock = await phrsDepositService['provider'].getBlockNumber();
      console.log(`当前区块: ${currentBlock}`);
      
      // 测试处理最近10个区块
      const testFromBlock = Math.max(currentBlock - 10, 0);
      console.log(`测试处理区块 ${testFromBlock} 到 ${currentBlock}`);
      
      await phrsDepositService.testProcessBlocks(testFromBlock, currentBlock);
      
    } catch (testError) {
      console.warn('⚠️  区块处理测试失败:', testError);
    }

    // 7. 测试并发处理（模拟）
    console.log('\n7. 测试并发处理能力...');
    const testPromises = [];
    for (let i = 0; i < 3; i++) {
      testPromises.push(
        phrsDepositService.healthCheck().then(result => {
          console.log(`   并发测试 ${i + 1}: ${result.status}`);
          return result;
        })
      );
    }
    
    const concurrentResults = await Promise.all(testPromises);
    const healthyCount = concurrentResults.filter(r => r.status === 'healthy').length;
    console.log(`✅ 并发测试完成: ${healthyCount}/${concurrentResults.length} 次健康检查成功`);

    console.log('\n🎉 测试完成！');
    console.log('================================================');
    console.log('✅ 所有测试项目已执行完毕');
    console.log('📊 测试结果总结:');
    console.log(`   - 数据库连接: 正常`);
    console.log(`   - 服务状态: ${status.isListening ? '运行中' : '已停止'}`);
    console.log(`   - 健康检查: ${healthCheck.status}`);
    console.log(`   - 现有充值记录: ${existingDeposits.length} 条`);
    console.log(`   - 用户钱包: ${userWallets.length} 个`);
    console.log(`   - 并发处理: ${healthyCount}/3 成功`);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testPhrsDepositService()
    .then(() => {
      console.log('\n✅ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试脚本执行失败:', error);
      process.exit(1);
    });
}

export { testPhrsDepositService };
