import { Request, Response } from 'express';
import { MyRequest } from '../types/customRequest';
import testResetService from '../services/testResetService';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';
import { logger } from '../utils/logger';
import { ajv, formatValidationErrors } from '../i18n';
import { NewTaskService } from '../services/NewTaskService';
import { UserTaskStatus } from '../models/UserTaskStatus';
import { TaskConfig } from '../models/TaskConfig';
import { UserWallet } from '../models/UserWallet';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { sequelize } from '../config/db';
import { Op } from 'sequelize';

/**
 * 测试重置控制器
 * 处理测试环境下的游戏状态重置请求
 * 仅在开发环境下可用
 */
class TestResetController {
  private taskService: NewTaskService;

  constructor() {
    this.taskService = new NewTaskService();
  }

  /**
   * 重置用户游戏状态
   * POST /api/test/reset-game-state
   */
  public async resetGameState(req: MyRequest, res: Response): Promise<void> {
    const startTime = Date.now();
    const requestId = `reset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // 记录环境信息（不再限制环境）
      logger.info('重置API请求环境信息', {
        requestId,
        environment: testResetService.getCurrentEnvironment(),
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });

      // 身份验证检查
      const walletId = Number(req.user?.walletId);
      if (!walletId) {
        logger.warn('重置游戏状态请求缺少用户身份验证', { requestId });
        res.status(401).json(errorResponse(
          tFromRequest(req, 'errors.unauthorized') || '用户未登录'
        ));
        return;
      }

      // 记录重置请求
      logger.info('收到重置游戏状态请求', {
        requestId,
        walletId,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        timestamp: new Date().toISOString()
      });

      // 验证重置前置条件
      const validationResult = await testResetService.validateResetPreconditions(walletId);

      logger.info('重置操作前置条件验证', {
        requestId,
        walletId,
        validationResult
      });

      // 如果验证失败，返回错误
      if (!validationResult.isValid) {
        logger.error('重置操作前置条件验证失败', {
          requestId,
          walletId,
          errors: validationResult.errors
        });

        res.status(400).json(errorResponse(
          validationResult.errors.join('; ')
        ));
        return;
      }

      // 如果有警告，记录但继续执行
      if (validationResult.warnings.length > 0) {
        logger.warn('重置操作警告', {
          requestId,
          walletId,
          warnings: validationResult.warnings
        });
      }

      // 获取重置前的状态信息
      const beforeResetInfo = await testResetService.getResetSafetyInfo(walletId);

      // 执行重置操作
      logger.info('开始执行游戏状态重置', {
        requestId,
        walletId,
        beforeReset: beforeResetInfo
      });

      const resetResult = await testResetService.resetUserGameState(walletId);

      const executionTime = Date.now() - startTime;

      // 获取重置后的状态信息
      const afterResetInfo = await testResetService.getResetSafetyInfo(walletId);

      // 记录审计日志
      testResetService.logResetAudit(
        walletId,
        'FULL_GAME_STATE_RESET',
        beforeResetInfo,
        afterResetInfo,
        {
          requestId,
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          timestamp: new Date()
        }
      );

      // 记录重置成功
      logger.info('游戏状态重置成功', {
        requestId,
        walletId,
        executionTime,
        result: {
          farmPlotsCount: resetResult.farmPlots.length,
          unlockedFarmPlots: resetResult.farmPlots.filter(p => p.isUnlocked).length,
          deliveryLineReset: !!resetResult.deliveryLine,
          resetTimestamp: resetResult.resetTimestamp
        },
        beforeState: beforeResetInfo,
        afterState: afterResetInfo
      });

      // 简化返回数据
      const responseData = {
        resetTimestamp: resetResult.resetTimestamp
      };

      res.json(successResponse(
        responseData,
        tFromRequest(req, 'success.gameStateReset') || '游戏状态重置成功'
      ));

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logger.error('重置游戏状态失败', {
        requestId,
        walletId: req.user?.walletId,
        executionTime,
        error: error instanceof Error ? {
          message: error.message,
          stack: error.stack
        } : error
      });

      // 根据错误类型返回不同的状态码
      let statusCode = 500;
      let errorMessage = tFromRequest(req, 'errors.serverError') || '服务器内部错误';

      if (error instanceof Error) {
        if (error.message.includes('用户钱包不存在')) {
          statusCode = 404;
          errorMessage = tFromRequest(req, 'errors.userNotFound') || '用户不存在';
        } else if (error.message.includes('权限')) {
          statusCode = 403;
          errorMessage = tFromRequest(req, 'errors.forbidden') || '权限不足';
        }
      }

      res.status(statusCode).json(errorResponse(errorMessage));
    }
  }

  /**
   * 获取重置操作的安全检查信息
   * GET /api/test/reset-safety-info
   */
  public async getResetSafetyInfo(req: MyRequest, res: Response): Promise<void> {
    try {
      // 记录环境信息（不再限制环境）
      logger.info('获取重置安全检查信息', {
        environment: testResetService.getCurrentEnvironment(),
        walletId: req.user?.walletId
      });

      // 身份验证检查
      const walletId = Number(req.user?.walletId);
      if (!walletId) {
        res.status(401).json(errorResponse(
          tFromRequest(req, 'errors.unauthorized') || '用户未登录'
        ));
        return;
      }

      const safetyInfo = await testResetService.getResetSafetyInfo(walletId);
      
      logger.info('获取重置安全检查信息', {
        walletId,
        safetyInfo
      });

      res.json(successResponse(safetyInfo));

    } catch (error) {
      logger.error('获取重置安全检查信息失败', {
        walletId: req.user?.walletId,
        error: error instanceof Error ? error.message : '未知错误'
      });

      res.status(500).json(errorResponse(
        tFromRequest(req, 'errors.serverError') || '服务器内部错误'
      ));
    }
  }

  /**
   * 健康检查接口
   * GET /api/test/health
   */
  public async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const healthInfo = {
        status: 'ok',
        environment: testResetService.getCurrentEnvironment(),
        isDevelopment: testResetService.getCurrentEnvironment() === 'development',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };

      res.json(successResponse(healthInfo));
    } catch (error) {
      res.status(500).json(errorResponse('健康检查失败'));
    }
  }

  /**
   * 重置用户任务状态
   * POST /api/test/reset-user-tasks
   */
  public async resetUserTasks(req: MyRequest, res: Response): Promise<void> {
    const requestId = `reset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    try {
      // 仅在开发环境下允许
      if (process.env.NODE_ENV !== 'development') {
        res.status(403).json(errorResponse('重置功能仅在开发环境下可用'));
        return;
      }

      const walletId = Number(req.user?.walletId);
      if (!walletId) {
        res.status(401).json(errorResponse('用户未登录'));
        return;
      }

      const { resetType = 'full_reset' } = req.body;
      let resetMessage = '';

      logger.info('开始重置用户任务', {
        requestId,
        walletId,
        resetType,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });

      const transaction = await sequelize.transaction();

      try {

        switch (resetType) {
          case 'tasks_only':
            // 仅重置任务状态
            await UserTaskStatus.destroy({
              where: { walletId },
              transaction
            });
            resetMessage = '任务状态已重置';
            break;

          case 'game_progress':
            // 重置游戏进度（牧场和流水线）
            await FarmPlot.update(
              {
                level: 1,
                isUnlocked: false
              },
              {
                where: { walletId, plotNumber: { [Op.gt]: 1 } },
                transaction
              }
            );

            // 保持第一个区域解锁
            await FarmPlot.update(
              { isUnlocked: true },
              {
                where: { walletId, plotNumber: 1 },
                transaction
              }
            );

            await DeliveryLine.update(
              { level: 1 },
              {
                where: { walletId },
                transaction
              }
            );

            resetMessage = '游戏进度已重置';
            break;

          case 'full_reset':
          default:
            // 完全重置：任务状态 + 游戏进度
            await UserTaskStatus.destroy({
              where: { walletId },
              transaction
            });

            await FarmPlot.update(
              {
                level: 1,
                isUnlocked: false
              },
              {
                where: { walletId, plotNumber: { [Op.gt]: 1 } },
                transaction
              }
            );

            // 保持第一个区域解锁
            await FarmPlot.update(
              { isUnlocked: true },
              {
                where: { walletId, plotNumber: 1 },
                transaction
              }
            );

            await DeliveryLine.update(
              { level: 1 },
              {
                where: { walletId },
                transaction
              }
            );

            resetMessage = '任务状态和游戏进度已完全重置';
            break;
        }

        await transaction.commit();

      } catch (error) {
        logger.error('事务执行失败，正在回滚', {
          requestId,
          walletId,
          resetType,
          error: error instanceof Error ? error.message : String(error)
        });
        await transaction.rollback();
        throw error;
      }

      // 事务提交成功后，重新初始化用户任务（在事务外执行）
      try {
        await this.taskService.initializeUserTasks(walletId);
        logger.info('任务初始化成功', { walletId, resetType });
      } catch (initError: any) {
        logger.warn('重置成功但任务初始化失败', {
          walletId,
          resetType,
          initError: initError.message
        });
        // 不抛出错误，因为重置操作已经成功
        // 可以在响应中添加警告信息
        resetMessage += ' (任务初始化可能需要手动触发)';
      }

      logger.info('用户任务重置成功', {
        requestId,
        walletId,
        resetType,
        message: resetMessage
      });

      res.json(successResponse({
        walletId,
        resetType,
        message: resetMessage
      }, '用户数据重置成功'));

    } catch (error: any) {
      logger.error('重置用户任务失败', {
        requestId,
        walletId: req.user?.walletId,
        error: error.message,
        stack: error.stack
      });
      res.status(500).json(errorResponse(error.message || '重置用户任务失败'));
    }
  }

  /**
   * 获取用户测试数据
   * GET /api/test/user-test-data
   */
  public async getUserTestData(req: MyRequest, res: Response): Promise<void> {
    try {
      // 仅在开发环境下允许
      if (process.env.NODE_ENV !== 'development') {
        res.status(403).json(errorResponse('测试接口仅在开发环境下可用'));
        return;
      }

      const walletId = Number(req.user?.walletId);
      if (!walletId) {
        res.status(401).json(errorResponse('用户未登录'));
        return;
      }

      // 获取用户基本信息
      const userWallet = await UserWallet.findByPk(walletId);
      if (!userWallet) {
        res.status(404).json(errorResponse('用户不存在'));
        return;
      }

      // 获取用户任务状态
      const userTasks = await UserTaskStatus.findAll({
        where: { walletId },
        include: [{
          model: TaskConfig,
          as: 'TaskConfig'
        }],
        order: [['taskId', 'ASC']]
      });

      // 获取用户游戏进度
      const farmPlots = await FarmPlot.findAll({
        where: { walletId },
        order: [['plotNumber', 'ASC']]
      });

      const deliveryLine = await DeliveryLine.findOne({
        where: { walletId }
      });

      // 获取邀请统计
      const inviteCount = await UserWallet.count({
        where: { referrerWalletId: walletId }
      });

      const responseData = {
        userInfo: {
          walletId: userWallet.id,
          walletAddress: userWallet.walletAddress,
          gems: userWallet.gem,
          milk: userWallet.milk,
          inviteCount
        },
        gameProgress: {
          farmPlots: farmPlots.map(plot => ({
            plotNumber: plot.plotNumber,
            level: plot.level,
            isUnlocked: plot.isUnlocked
          })),
          deliveryLine: deliveryLine ? {
            level: deliveryLine.level
          } : null
        },
        tasks: userTasks.map(task => ({
          taskId: task.taskId,
          status: task.status,
          currentProgress: task.currentProgress,
          targetProgress: task.targetProgress,
          taskConfig: {
            type: task.TaskConfig?.type,
            describe: task.TaskConfig?.describe,
            price1: task.TaskConfig?.price1,
            price2: task.TaskConfig?.price2
          }
        }))
      };

      logger.info('获取用户测试数据成功', {
        walletId,
        tasksCount: userTasks.length,
        farmPlotsCount: farmPlots.length
      });

      res.json(successResponse(responseData, '获取用户测试数据成功'));

    } catch (error: any) {
      logger.error('获取用户测试数据失败', {
        walletId: req.user?.walletId,
        error: error.message
      });
      res.status(500).json(errorResponse(error.message || '获取用户测试数据失败'));
    }
  }
}

export default new TestResetController();
