// 严格验证批量资源更新服务
// 实现更严格的三项验证逻辑：牛奶产量验证、牛奶消耗验证、宝石增加验证

import { Transaction } from 'sequelize';
import { sequelize } from '../config/db';
import { UserWallet } from '../models/UserWallet';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { createBigNumber, formatToThreeDecimalsNumber, formatToThreeDecimals, FarmPlotCalculator } from '../utils/bigNumberConfig';
import { BatchResourceUpdateService, BatchResourceUpdateRequest, MilkOperations } from './batchResourceUpdateService';
import { StrictValidationLogger, RequestLog } from '../utils/strictValidationLogger';
import { OfflineStatusManager } from '../utils/offlineStatusManager';
import dayjs from 'dayjs';

/**
 * 严格验证结果接口
 */
export interface StrictValidationResult {
  isValid: boolean;
  reason?: string;
  milkProductionValid: boolean;
  milkConsumptionValid: boolean;
  gemConversionValid: boolean;
  validationDetails: {
    milkProduction: {
      requested: number;
      calculated: number;
      maxAllowed: number;
      valid: boolean;
    };
    milkConsumption: {
      requested: number;
      calculated: number;
      maxAllowed: number;
      valid: boolean;
    };
    gemConversion: {
      requested: number;
      calculatedFromMilk: number;
      maxAllowed: number;
      valid: boolean;
      conversionRate: number;
    };
  };
}

/**
 * 严格验证批量资源更新响应数据
 */
export interface StrictBatchResourceUpdateResponse {
  success: boolean;
  data: {
    beforeUpdate: {
      gem: number;
      pendingMilk: number;
      lastActiveTime: string | null;
    };
    afterUpdate: {
      gem: number;
      pendingMilk: number;
      lastActiveTime: string;
    };
    changes: {
      usedStrictValidation: boolean; // 是否使用了严格验证
      validationPassed: boolean; // 严格验证是否通过
      fallbackToOldMethod: boolean; // 是否回退到旧方法
      timeWindowValid?: boolean; // 时间窗口是否有效
      timeWindowReason?: string; // 时间窗口失败原因
      lastActiveTimeUpdated?: boolean; // 是否更新了活跃时间
      strictValidationDetails?: StrictValidationResult; // 严格验证详情
      productionRates: {
        farmMilkPerCycle: number;
        deliveryBlockUnit: number;
        deliveryBlockPrice: number;
        timeElapsedSeconds: number;
      };
      details: {
        gem: {
          increased: number;
        };
        milk: {
          increased: number;
          decreased: number;
        };
      };
    };
    timestamp: string;
  };
  message: string;
}

/**
 * 严格验证批量资源更新服务类
 */
export class StrictBatchResourceUpdateService {
  
  /**
   * 严格验证批量资源更新主方法
   */
  static async updateResourcesWithStrictValidation(
    userId: number,
    walletId: number,
    request: BatchResourceUpdateRequest,
    language: string = 'zh'
  ): Promise<{ success: boolean; data: any; message: string }> {

    // 性能监控开始
    const startTime = Date.now();
    const performanceMetrics = {
      userId,
      walletId,
      startTime,
      phases: {} as Record<string, number>
    };

    const transaction = await sequelize.transaction();

    // 辅助函数：确保在所有退出路径都更新lastActiveTime
    const ensureLastActiveTimeUpdate = async (wallet: any, currentTransaction: any) => {
      try {
        if (wallet) {
          wallet.lastActiveTime = new Date();
          await wallet.save({ transaction: currentTransaction });
          console.log(`[活跃时间更新] 用户${userId} lastActiveTime已更新`);
        }
      } catch (updateError) {
        console.error(`[活跃时间更新] 用户${userId} 更新活跃时间失败:`, updateError);
      }
    };
    
    try {
      // 1. 基础参数验证（复用现有逻辑）
      performanceMetrics.phases.validationStart = Date.now();
      const validation = BatchResourceUpdateService.validateRequest(request);
      performanceMetrics.phases.validationEnd = Date.now();

      if (!validation.isValid) {
        // 即使参数验证失败，也要尝试更新lastActiveTime
        try {
          const wallet = await UserWallet.findOne({
            where: { id: walletId },
            transaction
          });
          await ensureLastActiveTimeUpdate(wallet, transaction);
          await transaction.commit();
        } catch (updateError) {
          console.error('参数验证失败时更新lastActiveTime失败:', updateError);
          await transaction.rollback();
        }

        // 记录验证失败的性能指标
        const totalTime = Date.now() - startTime;
        console.log(`[性能监控] 参数验证失败 - 用户${userId} - 耗时${totalTime}ms`);

        return {
          success: false,
          data: null,
          message: `参数验证失败: ${validation.errors.join(', ')}`
        };
      }

      // 2. 获取用户钱包信息
      performanceMetrics.phases.walletQueryStart = Date.now();
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        transaction
      });
      performanceMetrics.phases.walletQueryEnd = Date.now();

      if (!wallet) {
        await transaction.rollback();

        // 记录钱包不存在的性能指标
        const totalTime = Date.now() - startTime;
        console.log(`[性能监控] 钱包不存在 - 用户${userId} - 耗时${totalTime}ms`);

        return {
          success: false,
          data: null,
          message: '用户钱包不存在'
        };
      }

      // 3. 记录更新前状态
      const beforeUpdate = {
        gem: formatToThreeDecimalsNumber(wallet.gem || 0),
        pendingMilk: 0, // 将在获取delivery line后更新
        lastActiveTime: wallet.lastActiveTime ? dayjs(wallet.lastActiveTime).format('YYYY-MM-DD HH:mm:ss') : null
      };

      // 4. 获取或创建delivery line
      let deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });
      if (!deliveryLine) {
        deliveryLine = await DeliveryLine.create({
          walletId,
          level: 1,
          deliverySpeed: 1,
          blockUnit: 5,
          blockPrice: 5,
          upgradeCost: 500,
          lastDeliveryTime: new Date(),
          pendingMilk: 0,
          pendingBlocks: 0
        }, { transaction });
      }

      beforeUpdate.pendingMilk = formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0);

      // 5. 计算时间差和检查时间窗口
      const now = new Date();
      const timeWindowCheck = OfflineStatusManager.isInValidResourceUpdateWindow(wallet.lastActiveTime || null);
      const timeElapsedSeconds = timeWindowCheck.timeElapsedSeconds;
      const timeElapsedHours = timeElapsedSeconds / 3600;

      // 6. 检查时间窗口
      if (!timeWindowCheck.isValid) {
        // 时间窗口无效，但仍需要更新lastActiveTime以避免时间差累积
        const reason = timeWindowCheck.reason;

        wallet.lastActiveTime = now;
        await wallet.save({ transaction });

        // 提交事务（只更新时间，不更新资源）
        await transaction.commit();

        // 构建afterUpdate，显示时间已更新但资源未变化
        const afterUpdate = {
          gem: beforeUpdate.gem,
          pendingMilk: beforeUpdate.pendingMilk,
          lastActiveTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
        };

        return {
          success: true,
          data: {
            beforeUpdate,
            afterUpdate,
            changes: {
              usedStrictValidation: false, // 时间窗口无效，未进行严格验证
              validationPassed: false,     // 由于时间窗口问题，验证失败
              fallbackToOldMethod: false,  // 没有回退，直接拒绝
              timeWindowValid: false,      // 新增字段：时间窗口是否有效
              timeWindowReason: reason,    // 新增字段：时间窗口失败原因
              lastActiveTimeUpdated: true, // 新增字段：表示时间已更新
              productionRates: {
                farmMilkPerCycle: 0,
                deliveryBlockUnit: formatToThreeDecimalsNumber(deliveryLine.blockUnit),
                deliveryBlockPrice: formatToThreeDecimalsNumber(deliveryLine.blockPrice),
                timeElapsedSeconds
              },
              details: {
                gem: { increased: 0 },
                milk: { increased: 0, decreased: 0 }
              }
            },
            timestamp: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
          },
          message: `${reason}，已更新活跃时间`
        };
      }

      // 7. 执行严格验证
      const strictValidation = await this.performStrictValidation(
        request,
        walletId,
        timeElapsedHours,
        transaction,
        language
      );

      // 8. 根据验证结果决定处理方式
      let finalResult;
      
      if (strictValidation.isValid) {
        // 严格验证通过，使用前端请求的数值
        finalResult = await this.processWithFrontendValues(
          wallet,
          deliveryLine,
          request,
          beforeUpdate,
          strictValidation,
          timeElapsedSeconds,
          transaction,
          now
        );
      } else {
        // 严格验证失败，回退到旧接口计算方式
        finalResult = await this.fallbackToOldMethod(
          userId,
          walletId,
          request,
          beforeUpdate,
          strictValidation,
          timeElapsedSeconds,
          transaction,
          language,
          now
        );
      }

      await transaction.commit();

      // 记录成功完成的性能指标
      const totalTime = Date.now() - startTime;
      performanceMetrics.phases.totalTime = totalTime;

      // 详细的性能日志
      const validationTime = (performanceMetrics.phases.validationEnd || 0) - (performanceMetrics.phases.validationStart || 0);
      const walletQueryTime = (performanceMetrics.phases.walletQueryEnd || 0) - (performanceMetrics.phases.walletQueryStart || 0);

      console.log(`[性能监控] 严格验证批量资源更新完成 - 用户${userId}`, {
        totalTime: `${totalTime}ms`,
        validationTime: `${validationTime}ms`,
        walletQueryTime: `${walletQueryTime}ms`,
        validationPassed: finalResult.data?.changes?.validationPassed,
        fallbackUsed: finalResult.data?.changes?.fallbackToOldMethod,
        resourceChanges: {
          gem: finalResult.data?.changes?.details?.gem?.increased || 0,
          milk: {
            increased: finalResult.data?.changes?.details?.milk?.increased || 0,
            decreased: finalResult.data?.changes?.details?.milk?.decreased || 0
          }
        }
      });

      // 如果处理时间过长，记录警告
      if (totalTime > 1000) {
        console.warn(`[性能警告] 严格验证批量资源更新耗时过长: ${totalTime}ms - 用户${userId}`);
      }

      // 记录详细的请求日志
      const requestLog: RequestLog = {
        userId,
        walletId,
        timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        request,
        response: finalResult,
        processingTime: totalTime,
        validationResult: {
          passed: finalResult.data?.changes?.validationPassed || false,
          usedStrictValidation: finalResult.data?.changes?.usedStrictValidation || false,
          fallbackToOldMethod: finalResult.data?.changes?.fallbackToOldMethod || false,
          timeWindowValid: finalResult.data?.changes?.timeWindowValid,
          reason: finalResult.data?.changes?.timeWindowReason || finalResult.data?.changes?.strictValidationDetails?.reason
        },
        resourceChanges: {
          gem: finalResult.data?.changes?.details?.gem?.increased || 0,
          milkIncreased: finalResult.data?.changes?.details?.milk?.increased || 0,
          milkDecreased: finalResult.data?.changes?.details?.milk?.decreased || 0
        }
      };

      StrictValidationLogger.logRequest(requestLog);

      return finalResult;

    } catch (error) {
      // 即使发生异常，也要尝试更新lastActiveTime
      try {
        const errorUpdateTransaction = await sequelize.transaction();
        const wallet = await UserWallet.findOne({
          where: { id: walletId },
          transaction: errorUpdateTransaction
        });
        await ensureLastActiveTimeUpdate(wallet, errorUpdateTransaction);
        await errorUpdateTransaction.commit();
      } catch (updateError) {
        console.error('异常处理时更新lastActiveTime失败:', updateError);
      }

      await transaction.rollback();

      // 详细的错误日志记录
      const errorDetails = {
        userId,
        walletId,
        request,
        error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error,
        timestamp: new Date().toISOString()
      };

      console.error('严格验证批量资源更新失败:', errorDetails);

      // 记录错误到日志系统
      const errorProcessingTime = Date.now() - startTime;
      StrictValidationLogger.logError(userId, walletId, errorDetails, errorProcessingTime);

      // 根据错误类型返回不同的错误信息
      let errorMessage = '更新失败';
      let statusHint = 500;

      if (error instanceof Error) {
        if (error.message.includes('参数验证失败')) {
          errorMessage = '请求参数无效';
          statusHint = 400;
        } else if (error.message.includes('用户钱包不存在')) {
          errorMessage = '用户钱包未找到';
          statusHint = 404;
        } else if (error.message.includes('数据库')) {
          errorMessage = '数据库操作失败，请稍后重试';
          statusHint = 503;
        } else if (error.message.includes('网络') || error.message.includes('timeout')) {
          errorMessage = '网络超时，请稍后重试';
          statusHint = 408;
        } else {
          errorMessage = `系统错误: ${error.message}`;
        }
      }

      return {
        success: false,
        data: {
          statusHint, // 提示前端应该使用的HTTP状态码
          errorCode: 'STRICT_VALIDATION_ERROR', // 错误代码，便于前端处理
          timestamp: new Date().toISOString()
        },
        message: errorMessage
      };
    }
  }

  /**
   * 执行简化的严格验证 - 直接基于每秒速率计算
   */
  private static async performStrictValidation(
    request: BatchResourceUpdateRequest,
    walletId: number,
    timeElapsedHours: number,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<StrictValidationResult> {

    const timeInSeconds = formatToThreeDecimalsNumber(timeElapsedHours * 3600);

    // 获取农场和出货线数据
    let farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    // 如果用户没有解锁的农场区块，创建默认农场
    if (farmPlots.length === 0) {
      const defaultFarmPlot = await FarmPlot.create({
        walletId,
        plotNumber: 1,
        level: 1,
        isUnlocked: true,
        milkProduction: await FarmPlotCalculator.calculateBaseProduction(1, 1),
        productionSpeed: await FarmPlotCalculator.calculateProductionSpeed(1),
        barnCount: await FarmPlotCalculator.calculateBarnCount(1),
        upgradeCost: await FarmPlotCalculator.calculateUpgradeCostByLevel(1, 1),
        unlockCost: await FarmPlotCalculator.calculateUnlockCost(1),
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      }, { transaction });
      farmPlots = [defaultFarmPlot];
    }

    const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });
    if (!deliveryLine) {
      throw new Error('DeliveryLine not found');
    }

    // 获取VIP和加速道具效果
    const iapController = require('../controllers/iapController').default;
    const vipEffects = await iapController.getVipEffects(walletId);
    const boosterEffects = await iapController.getBoosterEffects(walletId);

    // 计算每秒生产速率（使用配置表）
    const milkProductionPerSecond = await this.calculateMilkProductionPerSecond(farmPlots, vipEffects);

    // 计算每秒消耗速率（理论最大值，使用配置表）
    const milkConsumptionPerSecond = await this.calculateMilkConsumptionPerSecond(deliveryLine, vipEffects, boosterEffects);

    // 计算牛奶到GEM的转换汇率（使用配置表）
    let milkToGemRate = 0;
    try {
      const { DeliveryLineConfig } = require('../models/DeliveryLineConfig');
      const config = await DeliveryLineConfig.getConfigByGrade(deliveryLine.level);
      if (config) {
        milkToGemRate = formatToThreeDecimalsNumber(config.profit / config.capacity);
      } else {
        console.warn(`⚠️ 等级${deliveryLine.level}的出货线配置不存在，使用数据库值`);
        milkToGemRate = formatToThreeDecimalsNumber(deliveryLine.blockPrice / deliveryLine.blockUnit);
      }
    } catch (error) {
      console.warn(`⚠️ 获取出货线配置失败，使用数据库值:`, error);
      milkToGemRate = formatToThreeDecimalsNumber(deliveryLine.blockPrice / deliveryLine.blockUnit);
    }

    // 获取前端请求值
    const requestedMilkProduction = request.milkOperations?.produce || 0;
    const requestedMilkConsumption = request.milkOperations?.consume || 0;
    const requestedGem = request.gemRequest || 0;

    // 简化验证逻辑：直接计算 (速率 × 时间间隔 × 3) 作为上限
    // 1. 牛奶产量 < 每秒产量 × 更新间隔 × 3
    const maxAllowedMilkProduction = formatToThreeDecimalsNumber(milkProductionPerSecond * timeInSeconds * 3);

    // 2. 牛奶消耗量 < 每秒消耗量 × 更新间隔 × 3
    const maxAllowedMilkConsumption = formatToThreeDecimalsNumber(milkConsumptionPerSecond * timeInSeconds * 3);

    // 3. 宝石增加验证：基于理论最大宝石产出能力
    // 如果用户请求消耗牛奶，则基于请求的牛奶消耗量验证
    // 如果用户不消耗牛奶，则基于理论最大消耗能力验证
    const baseGemCalculation = requestedMilkConsumption > 0
      ? requestedMilkConsumption * milkToGemRate  // 基于用户请求的牛奶消耗
      : milkConsumptionPerSecond * timeInSeconds * milkToGemRate; // 基于理论最大消耗能力
    const maxAllowedGemFromMilk = formatToThreeDecimalsNumber(baseGemCalculation * 3);

    // 计算理论值用于显示（不用于验证，仅用于响应数据）
    const theoreticalMilkProduction = formatToThreeDecimalsNumber(milkProductionPerSecond * timeInSeconds);
    const theoreticalMilkConsumption = formatToThreeDecimalsNumber(milkConsumptionPerSecond * timeInSeconds);
    const calculatedGemFromMilkConsumption = formatToThreeDecimalsNumber(baseGemCalculation);

    // 执行三项验证
    const milkProductionValid = requestedMilkProduction <= maxAllowedMilkProduction;
    const milkConsumptionValid = requestedMilkConsumption <= maxAllowedMilkConsumption;
    const gemConversionValid = requestedGem <= maxAllowedGemFromMilk;

    const isValid = milkProductionValid && milkConsumptionValid && gemConversionValid;

    // 构建验证结果
    const validationResult: StrictValidationResult = {
      isValid,
      milkProductionValid,
      milkConsumptionValid,
      gemConversionValid,
      validationDetails: {
        milkProduction: {
          requested: requestedMilkProduction,
          calculated: theoreticalMilkProduction,
          maxAllowed: maxAllowedMilkProduction,
          valid: milkProductionValid
        },
        milkConsumption: {
          requested: requestedMilkConsumption,
          calculated: theoreticalMilkConsumption,
          maxAllowed: maxAllowedMilkConsumption,
          valid: milkConsumptionValid
        },
        gemConversion: {
          requested: requestedGem,
          calculatedFromMilk: calculatedGemFromMilkConsumption,
          maxAllowed: maxAllowedGemFromMilk,
          valid: gemConversionValid,
          conversionRate: milkToGemRate
        }
      }
    };

    // 添加失败原因
    if (!isValid) {
      const reasons = [];
      if (!milkProductionValid) {
        reasons.push(`牛奶产量验证失败: 请求${requestedMilkProduction.toFixed(3)} > 允许${maxAllowedMilkProduction.toFixed(3)}`);
      }
      if (!milkConsumptionValid) {
        reasons.push(`牛奶消耗验证失败: 请求${requestedMilkConsumption.toFixed(3)} > 允许${maxAllowedMilkConsumption.toFixed(3)}`);
      }
      if (!gemConversionValid) {
        reasons.push(`宝石转换验证失败: 请求${requestedGem.toFixed(3)} > 允许${maxAllowedGemFromMilk.toFixed(3)}`);
      }
      validationResult.reason = reasons.join('; ');
    }

    return validationResult;
  }

  /**
   * 计算农场每秒牛奶产量（使用配置表）
   */
  private static async calculateMilkProductionPerSecond(farmPlots: any[], vipEffects: any): Promise<number> {
    let totalProductionPerSecond = 0;

    const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier || 1; // VIP 30% 生产速度加成

    // 获取配置服务
    const { FarmConfigService } = require('./farmConfigService');

    for (const plot of farmPlots) {
      try {
        // 从配置表获取实际数据
        const config = await FarmConfigService.getConfigByGrade(plot.level);
        if (!config) {
          console.warn(`⚠️ 等级${plot.level}的农场配置不存在，跳过该牧场区`);
          continue;
        }

        // 使用配置表的数据
        const baseProductionSpeed = 100.0 / config.speed; // speed是百分比，转换为秒
        const milkProduction = config.production;          // 每次产量
        const barnCount = config.cow;                      // 谷仓数量

        // 应用VIP加成：实际速度 = 基础速度 ÷ 加成倍率
        const actualProductionSpeed = baseProductionSpeed / productionSpeedMultiplier;

        // 每秒产量 = (每次产量 × 谷仓数量) ÷ 生产间隔
        const plotProductionPerSecond = (milkProduction * barnCount) / actualProductionSpeed;
        totalProductionPerSecond += plotProductionPerSecond;

      } catch (error) {
        console.warn(`⚠️ 获取等级${plot.level}的农场配置失败，使用数据库值:`, error);

        // 降级方案：使用数据库中的值
        const baseProductionSpeed = Number(plot.productionSpeed);
        const milkProduction = Number(plot.milkProduction);
        const barnCount = Number(plot.barnCount);
        const actualProductionSpeed = baseProductionSpeed / productionSpeedMultiplier;
        const plotProductionPerSecond = (milkProduction * barnCount) / actualProductionSpeed;
        totalProductionPerSecond += plotProductionPerSecond;
      }
    }

    return formatToThreeDecimalsNumber(totalProductionPerSecond);
  }

  /**
   * 计算出货线每秒牛奶消耗量（使用配置表）
   */
  private static async calculateMilkConsumptionPerSecond(deliveryLine: any, vipEffects: any, boosterEffects: any): Promise<number> {
    try {
      // 从配置表获取实际数据
      const { DeliveryLineConfig } = require('../models/DeliveryLineConfig');
      const config = await DeliveryLineConfig.getConfigByGrade(deliveryLine.level);

      if (!config) {
        console.warn(`⚠️ 等级${deliveryLine.level}的出货线配置不存在，使用数据库值`);
        // 降级方案：使用数据库中的值
        const baseDeliverySpeed = Number(deliveryLine.deliverySpeed);
        const blockUnit = Number(deliveryLine.blockUnit);
        const deliverySpeedMultiplier = (vipEffects.deliverySpeedMultiplier || 1) * (boosterEffects.speedMultiplier || 1);
        const actualDeliverySpeed = baseDeliverySpeed / deliverySpeedMultiplier;
        const consumptionPerSecond = blockUnit / actualDeliverySpeed;
        return formatToThreeDecimalsNumber(consumptionPerSecond);
      }

      // 使用配置表的数据
      const baseDeliverySpeed = Number(config.production_interval); // 基础处理间隔（秒）
      const blockUnit = Number(config.capacity);                    // 每次处理的牛奶量

      // 计算加成效果
      const deliverySpeedMultiplier = (vipEffects.deliverySpeedMultiplier || 1) * (boosterEffects.speedMultiplier || 1);

      // 应用加成：实际速度 = 基础速度 ÷ 加成倍率
      const actualDeliverySpeed = baseDeliverySpeed / deliverySpeedMultiplier;

      // 每秒消耗量 = 每次处理量 ÷ 处理间隔
      const consumptionPerSecond = blockUnit / actualDeliverySpeed;

      return formatToThreeDecimalsNumber(consumptionPerSecond);

    } catch (error) {
      console.warn(`⚠️ 获取等级${deliveryLine.level}的出货线配置失败，使用数据库值:`, error);

      // 降级方案：使用数据库中的值
      const baseDeliverySpeed = Number(deliveryLine.deliverySpeed);
      const blockUnit = Number(deliveryLine.blockUnit);
      const deliverySpeedMultiplier = (vipEffects.deliverySpeedMultiplier || 1) * (boosterEffects.speedMultiplier || 1);
      const actualDeliverySpeed = baseDeliverySpeed / deliverySpeedMultiplier;
      const consumptionPerSecond = blockUnit / actualDeliverySpeed;
      return formatToThreeDecimalsNumber(consumptionPerSecond);
    }
  }

  /**
   * 使用前端请求的数值进行资源更新（严格验证通过）
   */
  private static async processWithFrontendValues(
    wallet: any,
    deliveryLine: any,
    request: BatchResourceUpdateRequest,
    beforeUpdate: any,
    strictValidation: StrictValidationResult,
    timeElapsedSeconds: number,
    transaction: Transaction,
    now: Date
  ): Promise<{ success: boolean; data: any; message: string }> {

    // 使用前端请求的数值更新资源
    const gemIncrement = request.gemRequest || 0;
    const milkProduce = request.milkOperations?.produce || 0;
    const milkConsume = request.milkOperations?.consume || 0;

    // 更新GEM
    if (gemIncrement > 0) {
      const currentGemBN = createBigNumber(wallet.gem || 0);
      const newGemBN = currentGemBN.plus(gemIncrement);
      wallet.gem = newGemBN.toFixed(3);
    }

    // 更新牛奶
    const milkChange = milkProduce - milkConsume;
    if (milkChange !== 0) {
      const currentMilkBN = createBigNumber(deliveryLine.pendingMilk || 0);
      const newMilkBN = currentMilkBN.plus(milkChange);
      deliveryLine.pendingMilk = Math.max(0, formatToThreeDecimalsNumber(newMilkBN));
    }

    // 更新最后活跃时间
    wallet.lastActiveTime = now;

    // 保存更改
    await wallet.save({ transaction });
    await deliveryLine.save({ transaction });

    // 构建响应
    const afterUpdate = {
      gem: formatToThreeDecimalsNumber(wallet.gem || 0),
      pendingMilk: formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0),
      lastActiveTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
    };

    return {
      success: true,
      data: {
        beforeUpdate,
        afterUpdate,
        changes: {
          usedStrictValidation: true,
          validationPassed: true,
          fallbackToOldMethod: false,
          strictValidationDetails: strictValidation,
          productionRates: {
            farmMilkPerCycle: strictValidation.validationDetails.milkProduction.calculated,
            deliveryBlockUnit: deliveryLine.blockUnit,
            deliveryBlockPrice: deliveryLine.blockPrice,
            timeElapsedSeconds
          },
          details: {
            gem: { increased: gemIncrement },
            milk: {
              increased: milkProduce,
              decreased: milkConsume
            }
          }
        },
        timestamp: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
      },
      message: '严格验证通过，使用前端请求数值更新资源'
    };
  }

  /**
   * 回退到旧接口计算方式（严格验证失败）
   * 在当前事务中使用旧接口的计算逻辑，避免事务冲突
   */
  private static async fallbackToOldMethod(
    userId: number,
    walletId: number,
    request: BatchResourceUpdateRequest,
    beforeUpdate: any,
    strictValidation: StrictValidationResult,
    timeElapsedSeconds: number,
    transaction: Transaction,
    language: string,
    now: Date
  ): Promise<{ success: boolean; data: any; message: string }> {

    try {
      // 不回滚事务，而是在当前事务中使用旧接口的计算逻辑
      // 获取当前用户钱包和delivery line（已在事务中）
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        transaction
      });

      let deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      if (!deliveryLine) {
        deliveryLine = await DeliveryLine.create({
          walletId,
          level: 1,
          deliverySpeed: 1,
          blockUnit: 5,
          blockPrice: 5,
          upgradeCost: 500,
          lastDeliveryTime: new Date(),
          pendingMilk: 0,
          pendingBlocks: 0
        }, { transaction });
      }

      // 使用旧接口的协调计算逻辑
      const currentPendingMilk = formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0);
      const coordinatedResult = await BatchResourceUpdateService.calculateCoordinatedProduction(
        walletId,
        timeElapsedSeconds,
        currentPendingMilk,
        transaction
      );

      // 应用旧接口的计算结果
      const gemIncrement = coordinatedResult.gemProduced;
      const milkProduce = coordinatedResult.farmProduced;
      const milkConsume = coordinatedResult.deliveryConsumed;

      // 更新GEM
      if (gemIncrement > 0) {
        const currentGemBN = createBigNumber(wallet.gem || 0);
        const newGemBN = currentGemBN.plus(gemIncrement);
        wallet.gem = newGemBN.toFixed(3);
      }

      // 更新牛奶
      const milkChange = milkProduce - milkConsume;
      if (milkChange !== 0) {
        const currentMilkBN = createBigNumber(deliveryLine.pendingMilk || 0);
        const newMilkBN = currentMilkBN.plus(milkChange);
        deliveryLine.pendingMilk = Math.max(0, formatToThreeDecimalsNumber(newMilkBN));
      }

      // 更新最后活跃时间
      wallet.lastActiveTime = now;

      // 保存更改（在当前事务中）
      await wallet.save({ transaction });
      await deliveryLine.save({ transaction });

      // 构建响应
      const afterUpdate = {
        gem: formatToThreeDecimalsNumber(wallet.gem || 0),
        pendingMilk: formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0),
        lastActiveTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
      };

      return {
        success: true,
        data: {
          beforeUpdate,
          afterUpdate,
          changes: {
            usedStrictValidation: true,
            validationPassed: false,
            fallbackToOldMethod: true,
            strictValidationDetails: strictValidation,
            productionRates: {
              farmMilkPerCycle: coordinatedResult.farmProduced,
              deliveryBlockUnit: formatToThreeDecimalsNumber(deliveryLine.blockUnit),
              deliveryBlockPrice: formatToThreeDecimalsNumber(deliveryLine.blockPrice),
              timeElapsedSeconds
            },
            details: {
              gem: { increased: gemIncrement },
              milk: {
                increased: milkProduce,
                decreased: milkConsume
              }
            }
          },
          timestamp: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
        },
        message: `严格验证失败，已回退到旧方法计算: ${strictValidation.reason}`
      };

    } catch (error) {
      console.error('回退到旧方法失败:', error);
      throw error; // 让上层处理错误和事务回滚
    }
  }
}
