import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';

interface TaskConfigVersionAttributes {
  id: number;
  versionNumber: string;
  description?: string;
  configData: string;
  createdBy: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

interface TaskConfigVersionCreationAttributes extends Optional<TaskConfigVersionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class TaskConfigVersion extends Model<TaskConfigVersionAttributes, TaskConfigVersionCreationAttributes> implements TaskConfigVersionAttributes {
  public id!: number;
  public versionNumber!: string;
  public description?: string;
  public configData!: string;
  public createdBy!: string;
  public isActive!: boolean;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 获取配置数据（解析JSON）
   */
  public getParsedConfigData(): any[] {
    try {
      return JSON.parse(this.configData);
    } catch (error) {
      console.error('解析配置数据失败:', error);
      return [];
    }
  }

  /**
   * 设置配置数据（序列化为JSON）
   */
  public setConfigData(data: any[]): void {
    this.configData = JSON.stringify(data);
  }

  /**
   * 获取配置统计信息
   */
  public getConfigStats(): { totalTasks: number; tasksByType: { [key: number]: number } } {
    const data = this.getParsedConfigData();
    const stats = {
      totalTasks: data.length,
      tasksByType: {} as { [key: number]: number }
    };

    data.forEach((task: any) => {
      const type = task.type;
      stats.tasksByType[type] = (stats.tasksByType[type] || 0) + 1;
    });

    return stats;
  }

  /**
   * 激活此版本
   */
  public activate(): void {
    this.isActive = true;
  }

  /**
   * 停用此版本
   */
  public deactivate(): void {
    this.isActive = false;
  }

  /**
   * 获取版本信息的JSON表示
   */
  public toVersionJSON(): any {
    const stats = this.getConfigStats();
    return {
      id: this.id,
      versionNumber: this.versionNumber,
      description: this.description,
      createdBy: this.createdBy,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      stats
    };
  }
}

TaskConfigVersion.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    versionNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '版本号',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '版本描述',
    },
    configData: {
      type: DataTypes.TEXT('long'),
      allowNull: false,
      comment: '配置数据JSON',
    },
    createdBy: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '创建者',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否为当前活跃版本',
    },
  },
  {
    tableName: 'task_config_versions',
    sequelize,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['versionNumber']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['createdBy']
      }
    ]
  }
);
