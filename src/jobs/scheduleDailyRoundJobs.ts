// /src/jobs/scheduleDailyRoundJobs.ts
import { Queue } from "bullmq";
import IORedis from "ioredis";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { jobQueue } from "./bullmqConfig";
import { Session } from "../models";

import '../config/env'; // 导入统一的环境配置管理
import { Op } from "sequelize";

dayjs.extend(utc);
dayjs.extend(timezone);
const TZ = "Asia/Shanghai";

/**
 * 调度每天每个场次每个回合的抽奖任务
 *
 * 规则：假设场次分为两类：
 *  - 12:00场次：回合结果时间为：12:08、12:18、12:28
 *  - 20:00场次：回合结果时间为：20:08、20:18、20:28
 *
 * 我们为每个场次、每个回合生成一个 BullMQ 定时任务，CRON表达式按照出结果时间设置。
 */
export async function scheduleDailyRoundJobs() {
  console.log('[ScheduleJobs] 开始调度每日回合任务...');
  // 拿到session 表中今天的记录session_dt 等于 今天 20点或者12点的记录
  const today = dayjs().tz(TZ);
  console.log(`[ScheduleJobs] 正在查询 ${today.format('YYYY-MM-DD')} 的场次...`);
  
  const todaySessions = await Session.findAll({
    where: {
      session_dt: {
        [Op.and]: [
          { [Op.gte]: today.startOf("day").toDate() },
          { [Op.lte]: today.endOf("day").toDate() },
        ],
      },
    },
  });
  
  console.log(`[ScheduleJobs] 找到 ${todaySessions.length} 个场次`);
  
  // 每个场次3个回合
  const rounds = [1, 2, 3];

  for (const session of todaySessions) {
    console.log(`[ScheduleJobs] 正在处理场次: ${session.session_category}`);
    for (const round of rounds) {
      const resultMinute = 8 + (round - 1) * 10;
      const hour = parseInt(session.session_category.split(":")[0], 10);
      const pattern = `0 ${resultMinute} ${hour} * * *`;
      const schedulerId = `scheduler-${session.session_category}-round-${round}`;

      console.log(`[ScheduleJobs] 正在设置任务: ${schedulerId}, CRON: ${pattern}`);
      
      try {
        await jobQueue.upsertJobScheduler(
          schedulerId,
          {
            pattern,
            immediately: true,
          },
          {
            name: `lottery-result-job-${session.session_category}-round-${round}`,
            data: {
              session_category: session.session_category,
              roundIndex: round,
            },
            opts: {
              backoff: 3,
              attempts: 5,
              removeOnFail: 1000,
              removeOnComplete: 1000,
            },
          }
        );
        console.log(`[ScheduleJobs] 任务设置成功: ${schedulerId}`);
      } catch (error) {
        console.error(`[ScheduleJobs] 设置任务失败: ${schedulerId}`, error);
      }
    }
  }

  console.log('[ScheduleJobs] 所有任务调度完成');
}