set -e

echo "🔨 构建 Kaia Docker 镜像..."

# 备份当前 .dockerignore
if [ -f .dockerignore ]; then
    cp .dockerignore .dockerignore.backup
    echo "📋 已备份当前 .dockerignore 文件"
fi

# 使用 Kaia 专用的 .dockerignore
if [ -f .dockerignore.kaia ]; then
    cp .dockerignore.kaia .dockerignore
    echo "✅ 使用 Kaia 专用的 .dockerignore 文件"
else
    echo "⚠️  .dockerignore.kaia 不存在，使用默认配置"
fi

# 构建 Docker 镜像
docker build --no-cache --pull --rm -f Dockerfile.kaia -t moofun-kaia .

# 恢复原始 .dockerignore
if [ -f .dockerignore.backup ]; then
    mv .dockerignore.backup .dockerignore
    echo "🔄 已恢复原始 .dockerignore 文件"
fi

# 停止并删除旧的容器（如果存在）
docker stop moofun-kaia-container 2>/dev/null || true
docker rm moofun-kaia-container 2>/dev/null || true

docker image prune -f

# 运行新的容器实例
docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia

echo "🎮 初始化游戏配置数据..."
docker exec moofun-kaia-container ./scripts/init-configs-docker.sh kaia

echo "🗄️ 执行数据库迁移和种子数据初始化..."
docker exec moofun-kaia-container npm run seed:tasks:docker:kaia

echo "✅ Kaia 服务部署完成！"
echo "🌐 访问地址: http://localhost:9112/api"
echo "🔍 健康检查: http://localhost:9112/api/health/health"