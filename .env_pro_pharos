NODE_ENV=production
DB_NAME=wolf_pharos
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=mysql-8.3.0-wolf-shared
DB_PORT=3306

# API标识
API_INSTANCE=pharos-docker
API_NAME="Wolf Fun Pharos API (Docker)"

REDIS_HOST=redis2
REDIS_PORT=6379
REDIS_PASS=joetest1123
BOT_TOKEN="**********************************************"
PORT=3457
JWT_SECRET_Master=BugkKnehd77ddq6aoZmsddgoT6IfY23K9Kum5YQ
JWT_SECRET_Wallet=Siiuud9Ym3BuuusjsjslgGp1k10kWZUiOcTxE2nm5dw
TONCENTER_API_KEY=42a2067391ac48ffb6c3b96f6eb2a0209f6541caf5cbb368e9dd7ae6820dc4d2
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=dqcoeatpsoysbjcj
EMAIL_FROM='"MooFun" <<EMAIL>>'
ROOM_CAPACITY=20
TELEGRAM_BOT_TOKEN="**********************************************"
TEST_ETH_PRIVATE_KEY=0x740fe33880d587c4f2a2f1c0190f10ebe0a74afd6073a08da8d78c3c8a7cd39d
DAPP_PORTAL_CLIENT_ID=6390fd82-d5da-462f-84e9-4755ad33c04e
DAPP_PORTAL_CLIENT_SECRET=5c3719ec-7d5c-4769-8370-e85c57b21082
BASE_URL=https://pharos.jpegonapechain.com
KAIA_PRICE_UPDATE_SCHEDULE=0 */6 * * * *
KAIASCAN_API_KEY=634f4454-2afb-461a-b34a-fa198433539b

PHRS_DEPOSIT_CONTRACT_ADDRESS=******************************************
PHAROS_RPC_URL=https://api.zan.top/node/v1/pharos/testnet/be532fc2eea94b26ae071d45bc1f7896
PHRS_HISTORICAL_START_BLOCK=0

# PHRS兑换USD汇率（默认：1 PHRS = 0.0001 USD）
PHRS_TO_USD_RATE=0.1

# 每10分钟执行一次
PHRS_PRICE_UPDATE_SCHEDULE=0 */10 * * * *