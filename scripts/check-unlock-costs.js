#!/usr/bin/env node

/**
 * 检查农场区块解锁费用脚本
 */

const mysql = require('mysql2/promise');
require('../src/config/env'); // 导入统一的环境配置管理

async function checkUnlockCosts() {
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3669,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || 'root',
    database: process.env.DB_NAME || 'wolf_fun_db'
  };

  let connection;
  
  try {
    console.log('🔍 检查农场区块解锁费用...\n');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查询农场区块数据
    const [plots] = await connection.execute(`
      SELECT plotNumber, unlockCost, isUnlocked, walletId
      FROM farm_plots 
      WHERE walletId = 1
      ORDER BY plotNumber
    `);

    console.log('\n📊 当前农场区块解锁费用:');
    console.log('区块编号 | 解锁费用    | 是否已解锁');
    console.log('-'.repeat(35));

    // 预期的解锁费用（来自 FALLBACK_FARM_PLOT_UNLOCK_COST）
    const expectedUnlockCosts = [
      0, 378250, 1418625, 3405000, 13241250, 7566500, 15322500, 26484000, 
      62426250, 30645750, 55333125, 87399000, 170257500, 78697000, 135072000, 
      204312000, 359437500, 160801250, 268160625, 395385000
    ];

    let hasIncorrectCosts = false;

    for (const plot of plots) {
      const expected = expectedUnlockCosts[plot.plotNumber - 1];
      const actual = parseFloat(plot.unlockCost);
      const isCorrect = Math.abs(actual - expected) < 0.001;
      const status = plot.isUnlocked ? '已解锁' : '未解锁';
      const correctIcon = isCorrect ? '✅' : '❌';
      
      console.log(`${plot.plotNumber.toString().padStart(8)} | ${actual.toString().padStart(11)} | ${status.padStart(8)} ${correctIcon}`);
      
      if (!isCorrect) {
        hasIncorrectCosts = true;
        console.log(`         预期: ${expected}`);
      }
    }

    console.log('\n📋 总结:');
    if (hasIncorrectCosts) {
      console.log('❌ 发现不正确的解锁费用');
    } else {
      console.log('✅ 所有解锁费用都正确');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkUnlockCosts();
}

module.exports = checkUnlockCosts;