#!/usr/bin/env node

/**
 * 修复农场区块解锁费用脚本
 * 将解锁费用更新为使用统一的计算方法
 */

const mysql = require('mysql2/promise');
require('../src/config/env'); // 导入统一的环境配置管理

class UnlockCostFixer {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3669,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'root',
      database: process.env.DB_NAME || 'wolf_fun_db'
    };
  }

  /**
   * 创建数据库连接
   */
  async createConnection() {
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');
      return connection;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  /**
   * 计算正确的解锁费用
   * 使用与 FarmPlotCalculator.calculateUnlockCost() 相同的逻辑
   */
  calculateCorrectUnlockCost(plotNumber) {
    // 使用 FALLBACK_FARM_PLOT_UNLOCK_COST 数组
    const FALLBACK_FARM_PLOT_UNLOCK_COST = [
      0, 378250, 1418625, 3405000, 13241250, 7566500, 15322500, 26484000, 
      62426250, 30645750, 55333125, 87399000, 170257500, 78697000, 135072000, 
      204312000, 359437500, 160801250, 268160625, 395385000
    ];

    if (plotNumber < 1 || plotNumber > 20) {
      throw new Error(`Invalid plotNumber: ${plotNumber}. PlotNumber must be between 1 and 20.`);
    }
    
    return FALLBACK_FARM_PLOT_UNLOCK_COST[plotNumber - 1];
  }

  /**
   * 获取需要修复的农场区块
   */
  async getFarmPlotsToFix(connection) {
    try {
      const [plots] = await connection.execute(`
        SELECT id, walletId, plotNumber, unlockCost, isUnlocked
        FROM farm_plots 
        ORDER BY walletId, plotNumber
      `);
      
      console.log(`🔍 找到 ${plots.length} 个农场区块需要检查`);
      return plots;
    } catch (error) {
      console.error('❌ 获取农场区块失败:', error.message);
      throw error;
    }
  }

  /**
   * 检查区块是否需要更新解锁费用
   */
  needsUnlockCostUpdate(plot) {
    const correctUnlockCost = this.calculateCorrectUnlockCost(plot.plotNumber);
    const currentUnlockCost = parseFloat(plot.unlockCost);
    
    // 允许小的浮点数误差
    const tolerance = 0.001;
    return Math.abs(currentUnlockCost - correctUnlockCost) > tolerance;
  }

  /**
   * 更新农场区块的解锁费用
   */
  async updateUnlockCost(connection, plotId, correctUnlockCost) {
    try {
      await connection.execute(`
        UPDATE farm_plots 
        SET unlockCost = ?, updatedAt = NOW()
        WHERE id = ?
      `, [correctUnlockCost, plotId]);
      
      return true;
    } catch (error) {
      console.error(`❌ 更新区块 ${plotId} 解锁费用失败:`, error.message);
      return false;
    }
  }

  /**
   * 执行修复
   */
  async fix(dryRun = false) {
    let connection;
    
    try {
      console.log('🚀 开始修复农场区块解锁费用...\n');
      console.log(`模式: ${dryRun ? '预览模式（不会实际更新）' : '实际更新模式'}`);
      console.log('='.repeat(60));

      // 1. 创建数据库连接
      connection = await this.createConnection();

      // 2. 获取需要修复的农场区块
      const plots = await this.getFarmPlotsToFix(connection);

      // 3. 分析和更新
      let totalChecked = 0;
      let totalNeedsUpdate = 0;
      let totalUpdated = 0;
      let totalErrors = 0;

      console.log('\n📋 开始分析农场区块解锁费用...\n');

      for (const plot of plots) {
        totalChecked++;
        
        // 计算正确的解锁费用
        const correctUnlockCost = this.calculateCorrectUnlockCost(plot.plotNumber);
        
        // 检查是否需要更新
        if (this.needsUnlockCostUpdate(plot)) {
          totalNeedsUpdate++;
          
          console.log(`🔄 区块解锁费用需要更新:`);
          console.log(`   用户: ${plot.walletId}, 区块: ${plot.plotNumber}`);
          console.log(`   当前解锁费用: ${plot.unlockCost}`);
          console.log(`   正确解锁费用: ${correctUnlockCost}`);
          console.log(`   是否已解锁: ${plot.isUnlocked ? '是' : '否'}`);
          
          if (!dryRun) {
            const success = await this.updateUnlockCost(connection, plot.id, correctUnlockCost);
            if (success) {
              totalUpdated++;
              console.log(`   ✅ 更新成功`);
            } else {
              totalErrors++;
              console.log(`   ❌ 更新失败`);
            }
          } else {
            console.log(`   📝 预览模式 - 将会更新`);
          }
          console.log('');
        }
      }

      // 4. 输出统计结果
      console.log('='.repeat(60));
      console.log('📊 修复统计:');
      console.log(`   检查的区块: ${totalChecked}`);
      console.log(`   需要更新解锁费用: ${totalNeedsUpdate}`);
      
      if (!dryRun) {
        console.log(`   成功更新: ${totalUpdated}`);
        console.log(`   更新失败: ${totalErrors}`);
        
        if (totalUpdated > 0) {
          console.log('\n✅ 解锁费用修复完成！');
        }
      } else {
        console.log('\n📝 预览完成！使用 --execute 参数执行实际更新');
      }

      return {
        totalChecked,
        totalNeedsUpdate,
        totalUpdated,
        totalErrors
      };

    } catch (error) {
      console.error('❌ 修复失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  
  if (dryRun) {
    console.log('⚠️  预览模式：将显示需要更新的内容，但不会实际修改数据');
    console.log('💡 使用 --execute 参数执行实际更新');
    console.log('');
  }

  const fixer = new UnlockCostFixer();

  try {
    const result = await fixer.fix(dryRun);
    
    if (dryRun && result.totalNeedsUpdate > 0) {
      console.log('\n🔄 要执行实际更新，请运行:');
      console.log('node scripts/fix-unlock-costs.js --execute');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = UnlockCostFixer;