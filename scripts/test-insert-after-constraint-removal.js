const { Sequelize } = require('sequelize');
require('../src/config/env');

async function testInsertAfterConstraintRemoval() {
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    console.log('🧪 测试删除外键约束后的INSERT操作...\n');

    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 1. 验证约束确实已删除
      console.log('1. 验证外键约束状态:');
      const [constraints] = await sequelize.query(`
        SELECT CONSTRAINT_NAME 
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'user_wallets' 
          AND COLUMN_NAME = 'referrerWalletId'
          AND REFERENCED_TABLE_NAME IS NOT NULL
      `, { transaction });

      if (constraints.length === 0) {
        console.log('✅ 确认外键约束已删除');
      } else {
        console.log('❌ 外键约束仍然存在:');
        console.table(constraints);
        await transaction.rollback();
        return;
      }

      // 2. 测试插入有前向引用的记录
      console.log('\n2. 测试插入前向引用记录:');
      
      // 先删除可能存在的测试记录
      await sequelize.query(`
        DELETE FROM user_wallets WHERE id IN (998, 999)
      `, { transaction });

      // 测试插入ID 999，引用ID 998（还未插入）
      console.log('   插入ID 999，引用ID 998（前向引用）...');
      await sequelize.query(`
        INSERT INTO user_wallets (id, userId, code, referrerWalletId) 
        VALUES (999, 999, 'TEST999', 998)
      `, { transaction });
      console.log('✅ 前向引用插入成功！');

      // 然后插入ID 998
      console.log('   插入ID 998...');
      await sequelize.query(`
        INSERT INTO user_wallets (id, userId, code, referrerWalletId) 
        VALUES (998, 998, 'TEST998', NULL)
      `, { transaction });
      console.log('✅ 被引用记录插入成功！');

      // 3. 测试插入无效引用的记录
      console.log('\n3. 测试插入无效引用记录:');
      console.log('   插入ID 997，引用不存在的ID 888...');
      await sequelize.query(`
        INSERT INTO user_wallets (id, userId, code, referrerWalletId) 
        VALUES (997, 997, 'TEST997', 888)
      `, { transaction });
      console.log('✅ 无效引用插入成功！');

      // 4. 验证插入的数据
      console.log('\n4. 验证插入的测试数据:');
      const [testRecords] = await sequelize.query(`
        SELECT id, userId, code, referrerWalletId 
        FROM user_wallets 
        WHERE id IN (997, 998, 999)
        ORDER BY id
      `, { transaction });

      console.table(testRecords);

      // 清理测试数据
      console.log('\n5. 清理测试数据...');
      await sequelize.query(`
        DELETE FROM user_wallets WHERE id IN (997, 998, 999)
      `, { transaction });
      console.log('✅ 测试数据已清理');

      // 提交事务
      await transaction.commit();
      console.log('\n🎉 测试完成！外键约束删除成功，INSERT操作正常！');

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testInsertAfterConstraintRemoval().catch(console.error);
