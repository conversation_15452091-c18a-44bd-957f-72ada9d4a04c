#!/bin/bash

# Docker Compose 管理脚本
set -e

KAIA_COMPOSE="docker-compose-kaia.yml"
PHAROS_COMPOSE="docker-compose.pharos.yml"

show_help() {
    echo "🐳 Docker Compose 管理脚本"
    echo ""
    echo "用法: $0 [命令] [服务]"
    echo ""
    echo "命令:"
    echo "  start-all     启动所有服务"
    echo "  stop-all      停止所有服务"
    echo "  restart-all   重启所有服务"
    echo "  start-kaia    启动 Kaia 服务"
    echo "  stop-kaia     停止 Kaia 服务"
    echo "  start-pharos  启动 Pharos 服务"
    echo "  stop-pharos   停止 Pharos 服务"
    echo "  status        查看服务状态"
    echo "  logs          查看日志"
    echo "  clean         清理未使用的容器和网络"
    echo ""
    echo "示例:"
    echo "  $0 start-all"
    echo "  $0 logs kaia"
    echo "  $0 status"
}

start_all() {
    echo "🚀 启动所有服务..."
    
    # 先启动 Kaia (包含MySQL)
    echo "📊 启动 Kaia 服务 (包含共享MySQL)..."
    docker compose -f $KAIA_COMPOSE up -d
    
    # 等待MySQL启动完成
    echo "⏳ 等待MySQL启动完成..."
    sleep 10
    
    # 启动 Pharos
    echo "🔮 启动 Pharos 服务..."
    docker compose -f $PHAROS_COMPOSE up -d
    
    echo "✅ 所有服务启动完成！"
    show_status
}

stop_all() {
    echo "🛑 停止所有服务..."
    docker compose -f $PHAROS_COMPOSE down
    docker compose -f $KAIA_COMPOSE down
    echo "✅ 所有服务已停止！"
}

restart_all() {
    echo "🔄 重启所有服务..."
    stop_all
    sleep 2
    start_all
}

start_kaia() {
    echo "📊 启动 Kaia 服务..."
    docker compose -f $KAIA_COMPOSE up -d
    echo "✅ Kaia 服务启动完成！"
}

stop_kaia() {
    echo "🛑 停止 Kaia 服务..."
    docker compose -f $KAIA_COMPOSE down
    echo "✅ Kaia 服务已停止！"
}

start_pharos() {
    echo "🔮 启动 Pharos 服务..."
    docker compose -f $PHAROS_COMPOSE up -d
    echo "✅ Pharos 服务启动完成！"
}

stop_pharos() {
    echo "🛑 停止 Pharos 服务..."
    docker compose -f $PHAROS_COMPOSE down
    echo "✅ Pharos 服务已停止！"
}

show_status() {
    echo "📊 服务状态:"
    echo ""
    echo "=== Kaia 服务 ==="
    docker compose -f $KAIA_COMPOSE ps
    echo ""
    echo "=== Pharos 服务 ==="
    docker compose -f $PHAROS_COMPOSE ps
}

show_logs() {
    if [ -z "$2" ]; then
        echo "📝 显示所有日志..."
        docker compose -f $KAIA_COMPOSE logs -f &
        docker compose -f $PHAROS_COMPOSE logs -f &
        wait
    elif [ "$2" = "kaia" ]; then
        echo "📝 显示 Kaia 日志..."
        docker compose -f $KAIA_COMPOSE logs -f
    elif [ "$2" = "pharos" ]; then
        echo "📝 显示 Pharos 日志..."
        docker compose -f $PHAROS_COMPOSE logs -f
    else
        echo "❌ 未知服务: $2"
        echo "可用服务: kaia, pharos"
    fi
}

clean_docker() {
    echo "🧹 清理Docker资源..."
    docker system prune -f
    docker volume prune -f
    echo "✅ 清理完成！"
}

# 主逻辑
case "$1" in
    start-all)
        start_all
        ;;
    stop-all)
        stop_all
        ;;
    restart-all)
        restart_all
        ;;
    start-kaia)
        start_kaia
        ;;
    stop-kaia)
        stop_kaia
        ;;
    start-pharos)
        start_pharos
        ;;
    stop-pharos)
        stop_pharos
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    clean)
        clean_docker
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
