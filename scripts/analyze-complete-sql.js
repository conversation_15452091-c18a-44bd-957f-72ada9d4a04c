const fs = require('fs');
const path = require('path');

// 解析SQL文件中的INSERT数据
function parseCompleteSQL() {
  console.log('🔍 分析完整SQL文件中的外键约束问题...\n');

  // 从SQL文件中提取的数据（简化版，只包含ID和referrerWalletId）
  const records = [
    {id: 1, referrerWalletId: 7, code: '1F50B6'},
    {id: 2, referrerWalletId: null, code: 'A6D924'},
    {id: 3, referrerWalletId: 6, code: '8A96F9'},
    {id: 4, referrerWalletId: null, code: '66B741'},
    {id: 5, referrerWalletId: null, code: 'ECD5EE'},
    {id: 6, referrerWalletId: null, code: 'C320A2'},
    {id: 7, referrerWalletId: null, code: '6F409B'},
    {id: 8, referrerWalletId: null, code: '65DDBD'},
    {id: 9, referrerWalletId: null, code: 'F57FE1'},
    {id: 10, referrerWalletId: null, code: 'D92131'},
    {id: 11, referrerWalletId: null, code: '1D7A24'},
    {id: 12, referrerWalletId: null, code: '6A4006'},
    {id: 13, referrerWalletId: 5, code: '1A290A'},
    {id: 14, referrerWalletId: null, code: '6EE2EE'},
    {id: 15, referrerWalletId: 7, code: '688068'},
    {id: 16, referrerWalletId: null, code: '215E86'},
    {id: 17, referrerWalletId: 7, code: '1B2A4E'},
    {id: 18, referrerWalletId: 17, code: '4AA854'},
    {id: 19, referrerWalletId: null, code: '4351D5'},
    {id: 20, referrerWalletId: null, code: 'BDF7E9'},
    {id: 21, referrerWalletId: 5, code: 'DA4001'},
    {id: 22, referrerWalletId: 5, code: '6A8D9D'},
    {id: 23, referrerWalletId: null, code: 'BE3FAE'},
    {id: 24, referrerWalletId: null, code: '964510'},
    {id: 25, referrerWalletId: 29, code: '97CB0A'},
    {id: 26, referrerWalletId: null, code: '61DAFB'},
    {id: 27, referrerWalletId: null, code: '273FB8'},
    {id: 28, referrerWalletId: null, code: '4F88C4'},
    {id: 29, referrerWalletId: null, code: '9B12F9'},
    {id: 30, referrerWalletId: null, code: '1FC402'},
    {id: 31, referrerWalletId: null, code: 'EA8F26'},
    {id: 32, referrerWalletId: null, code: '31B81E'},
    {id: 33, referrerWalletId: null, code: '96ADE1'},
    {id: 34, referrerWalletId: null, code: '587817'},
    {id: 35, referrerWalletId: null, code: 'B4DDFC'},
    {id: 36, referrerWalletId: null, code: '143C92'},
    {id: 37, referrerWalletId: null, code: '0BE911'},
    {id: 38, referrerWalletId: null, code: '066324'},
    {id: 39, referrerWalletId: null, code: 'BC521B'},
    {id: 40, referrerWalletId: null, code: 'D17843'},
    {id: 41, referrerWalletId: null, code: 'C27222'},
    {id: 42, referrerWalletId: 33, code: '502F29'},
    {id: 43, referrerWalletId: null, code: '8E09B3'},
    {id: 44, referrerWalletId: null, code: '93F5DD'},
    {id: 45, referrerWalletId: null, code: 'E19791'},
    {id: 46, referrerWalletId: null, code: '041D86'},
    {id: 47, referrerWalletId: null, code: '464C16'},
    {id: 48, referrerWalletId: null, code: '3CE1EA'},
    {id: 49, referrerWalletId: null, code: '6A98F4'},
    {id: 50, referrerWalletId: null, code: 'EBE8B8'},
    {id: 51, referrerWalletId: null, code: '2E7212'},
    {id: 52, referrerWalletId: null, code: '74540F'},
    {id: 53, referrerWalletId: null, code: '93A366'},
    {id: 54, referrerWalletId: null, code: '0808A9'},
    {id: 55, referrerWalletId: null, code: 'EFFE8E'}
  ];

  // 1. 检查前向引用问题
  const forwardReferences = [];
  const existingIds = new Set();
  
  for (const record of records) {
    if (record.referrerWalletId !== null && !existingIds.has(record.referrerWalletId)) {
      forwardReferences.push({
        id: record.id,
        referrerWalletId: record.referrerWalletId,
        problem: `引用ID ${record.referrerWalletId}，但该ID在后面才插入`
      });
    }
    existingIds.add(record.id);
  }

  if (forwardReferences.length > 0) {
    console.log('❌ 发现前向引用问题:');
    console.table(forwardReferences);
  } else {
    console.log('✅ 未发现前向引用问题');
  }

  // 2. 检查无效引用
  const allIds = new Set(records.map(r => r.id));
  const invalidReferences = [];
  
  for (const record of records) {
    if (record.referrerWalletId !== null && !allIds.has(record.referrerWalletId)) {
      invalidReferences.push({
        id: record.id,
        referrerWalletId: record.referrerWalletId,
        problem: `引用不存在的ID ${record.referrerWalletId}`
      });
    }
  }

  if (invalidReferences.length > 0) {
    console.log('\n❌ 发现无效引用问题:');
    console.table(invalidReferences);
  } else {
    console.log('\n✅ 未发现无效引用问题');
  }

  // 3. 生成正确的插入顺序
  console.log('\n🔧 生成修复方案:\n');
  
  const sortedRecords = [];
  const remaining = [...records];
  let batch = 1;

  while (remaining.length > 0) {
    const currentBatch = [];
    const nextRemaining = [];

    for (const record of remaining) {
      if (record.referrerWalletId === null || 
          sortedRecords.some(r => r.id === record.referrerWalletId)) {
        currentBatch.push(record);
      } else {
        nextRemaining.push(record);
      }
    }

    if (currentBatch.length === 0) {
      console.log('⚠️ 检测到无法解决的依赖关系，将以下记录的referrerWalletId设为NULL:');
      const problematicIds = nextRemaining.map(r => r.id);
      console.log(`ID: ${problematicIds.join(', ')}`);
      
      for (const record of nextRemaining) {
        record.referrerWalletId = null;
        currentBatch.push(record);
      }
      nextRemaining.length = 0;
    }

    if (currentBatch.length > 0) {
      console.log(`\n-- 第${batch}批插入 (${currentBatch.length}条记录):`);
      console.log(`ID范围: ${Math.min(...currentBatch.map(r => r.id))} - ${Math.max(...currentBatch.map(r => r.id))}`);
      
      // 显示这批记录的ID列表
      const batchIds = currentBatch.map(r => r.id).sort((a, b) => a - b);
      console.log(`记录ID: ${batchIds.join(', ')}`);
      
      sortedRecords.push(...currentBatch);
      batch++;
    }

    remaining.splice(0, remaining.length, ...nextRemaining);
  }

  return { records, forwardReferences, invalidReferences, sortedRecords };
}

// 运行分析
const result = parseCompleteSQL();

console.log('\n📊 统计信息:');
console.log(`总记录数: ${result.records.length}`);
console.log(`前向引用问题: ${result.forwardReferences.length}`);
console.log(`无效引用问题: ${result.invalidReferences.length}`);
console.log(`需要分批插入: ${result.forwardReferences.length > 0 ? '是' : '否'}`);

if (result.forwardReferences.length > 0) {
  console.log('\n💡 解决建议:');
  console.log('1. 使用上面生成的分批插入顺序');
  console.log('2. 或者将有问题的referrerWalletId设置为NULL');
  console.log('3. 建议在事务中执行以确保数据一致性');
}
