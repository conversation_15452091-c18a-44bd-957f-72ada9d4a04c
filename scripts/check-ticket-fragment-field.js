const { Sequelize } = require('sequelize');
require('../src/config/env');

async function checkTicketFragmentField() {
  console.log('🔍 检查 user_wallets 表中的 ticket_fragment 字段...\n');

  // 检查 Kaia 数据库
  console.log('📊 检查 Kaia 数据库 (wolf_kaia)...');
  const kaiaSequelize = new Sequelize(
    'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    await kaiaSequelize.authenticate();
    console.log('✅ Kaia 数据库连接成功');

    const [kaiaFields] = await kaiaSequelize.query(`
      DESCRIBE user_wallets
    `);

    const kaiaTicketFragment = kaiaFields.find(field => field.Field === 'ticket_fragment');
    
    if (kaiaTicketFragment) {
      console.log('✅ Kaia 数据库中存在 ticket_fragment 字段');
      console.log('   类型:', kaiaTicketFragment.Type);
      console.log('   默认值:', kaiaTicketFragment.Default);
      console.log('   允许NULL:', kaiaTicketFragment.Null);
    } else {
      console.log('❌ Kaia 数据库中不存在 ticket_fragment 字段');
    }

  } catch (error) {
    console.error('❌ Kaia 数据库检查失败:', error.message);
  } finally {
    await kaiaSequelize.close();
  }

  console.log('');

  // 检查 Pharos 数据库
  console.log('📊 检查 Pharos 数据库 (wolf_pharos)...');
  const pharosSequelize = new Sequelize(
    'wolf_pharos',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    await pharosSequelize.authenticate();
    console.log('✅ Pharos 数据库连接成功');

    const [pharosFields] = await pharosSequelize.query(`
      DESCRIBE user_wallets
    `);

    const pharosTicketFragment = pharosFields.find(field => field.Field === 'ticket_fragment');
    
    if (pharosTicketFragment) {
      console.log('✅ Pharos 数据库中存在 ticket_fragment 字段');
      console.log('   类型:', pharosTicketFragment.Type);
      console.log('   默认值:', pharosTicketFragment.Default);
      console.log('   允许NULL:', pharosTicketFragment.Null);
    } else {
      console.log('❌ Pharos 数据库中不存在 ticket_fragment 字段');
    }

  } catch (error) {
    console.error('❌ Pharos 数据库检查失败:', error.message);
  } finally {
    await pharosSequelize.close();
  }

  console.log('');

  // 显示所有字段
  console.log('📋 显示 user_wallets 表的所有字段...');
  
  const checkSequelize = new Sequelize(
    'wolf_kaia',
    process.env.DB_USER || 'wolf',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3669,
      dialect: 'mysql',
      logging: false
    }
  );

  try {
    const [allFields] = await checkSequelize.query(`
      DESCRIBE user_wallets
    `);

    console.log('字段列表:');
    allFields.forEach((field, index) => {
      const highlight = field.Field === 'ticket_fragment' ? '👉 ' : '   ';
      console.log(`${highlight}${index + 1}. ${field.Field} (${field.Type})`);
    });

  } catch (error) {
    console.error('❌ 字段列表获取失败:', error.message);
  } finally {
    await checkSequelize.close();
  }
}

// 运行检查
checkTicketFragmentField().catch(console.error);
