#!/bin/bash

# Wolf Fun 代码更新部署脚本
# 用于服务器生产环境的快速代码更新，保持数据安全，最小化停机时间

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Wolf Fun 代码更新部署脚本${NC}"
    echo ""
    echo "用法: $0 [服务] [选项]"
    echo ""
    echo "服务:"
    echo "  kaia      只更新 Kaia API 服务"
    echo "  pharos    只更新 Pharos API 服务"
    echo "  both      更新两个服务（默认）"
    echo ""
    echo "选项:"
    echo "  --skip-git       跳过 Git 代码拉取"
    echo "  --skip-backup    跳过镜像备份"
    echo "  --no-rollback    禁用自动回滚功能"
    echo "  --force          强制更新（跳过确认）"
    echo "  --dry-run        只显示将要执行的操作"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                       # 更新两个服务"
    echo "  $0 kaia                  # 只更新 Kaia 服务"
    echo "  $0 both --skip-git       # 更新两个服务但跳过代码拉取"
    echo "  $0 pharos --dry-run      # 预览 Pharos 更新操作"
}

# 检查 Docker 和 Git
check_prerequisites() {
    log_step "检查系统环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 检查当前服务状态
check_current_status() {
    log_step "检查当前服务状态..."
    
    local kaia_status=$(docker inspect --format='{{.State.Status}}' moofun-kaia-container 2>/dev/null || echo "not_found")
    local pharos_status=$(docker inspect --format='{{.State.Status}}' moofun-pharos-container 2>/dev/null || echo "not_found")
    
    echo "📊 当前服务状态:"
    echo "  🔹 Kaia 容器: $kaia_status"
    echo "  🔹 Pharos 容器: $pharos_status"
    
    # 检查健康状态
    if [ "$kaia_status" = "running" ]; then
        if curl -f http://localhost:9112/api/health/health &>/dev/null; then
            echo "  ✅ Kaia API: 健康"
        else
            echo "  ❌ Kaia API: 不健康"
        fi
    fi
    
    if [ "$pharos_status" = "running" ]; then
        if curl -f http://localhost:9113/api/health/health &>/dev/null; then
            echo "  ✅ Pharos API: 健康"
        else
            echo "  ❌ Pharos API: 不健康"
        fi
    fi
}

# 拉取最新代码
pull_latest_code() {
    if [ "$SKIP_GIT" = true ]; then
        log_warning "跳过 Git 代码拉取"
        return 0
    fi
    
    log_step "拉取最新代码..."
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_warning "检测到未提交的更改"
        if [ "$FORCE" != true ]; then
            read -p "是否继续？这可能会覆盖本地更改 (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "取消更新"
                exit 0
            fi
        fi
    fi
    
    # 获取当前分支和提交
    local current_branch=$(git branch --show-current)
    local current_commit=$(git rev-parse HEAD)
    
    log_info "当前分支: $current_branch"
    log_info "当前提交: ${current_commit:0:8}"
    
    # 拉取最新代码
    if git pull origin "$current_branch"; then
        local new_commit=$(git rev-parse HEAD)
        if [ "$current_commit" = "$new_commit" ]; then
            log_info "代码已是最新版本"
        else
            log_success "代码更新完成: ${current_commit:0:8} -> ${new_commit:0:8}"
            # 保存提交信息用于回滚
            echo "$current_commit" > .last_deploy_commit
        fi
    else
        log_error "代码拉取失败"
        exit 1
    fi
}

# 备份当前镜像
backup_images() {
    if [ "$SKIP_BACKUP" = true ]; then
        log_warning "跳过镜像备份"
        return 0
    fi
    
    log_step "备份当前镜像..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        if docker images -q moofun-kaia:latest &>/dev/null; then
            docker tag moofun-kaia:latest moofun-kaia:backup_$timestamp
            log_success "Kaia 镜像备份完成: moofun-kaia:backup_$timestamp"
            echo "moofun-kaia:backup_$timestamp" > .last_kaia_backup
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        if docker images -q moofun-pharos:latest &>/dev/null; then
            docker tag moofun-pharos:latest moofun-pharos:backup_$timestamp
            log_success "Pharos 镜像备份完成: moofun-pharos:backup_$timestamp"
            echo "moofun-pharos:backup_$timestamp" > .last_pharos_backup
        fi
    fi
}

# 构建新镜像
build_new_images() {
    log_step "构建新镜像..."
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        log_info "构建 Kaia 镜像..."
        if ./scripts/docker-build.sh kaia; then
            log_success "Kaia 镜像构建完成"
        else
            log_error "Kaia 镜像构建失败"
            return 1
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        log_info "构建 Pharos 镜像..."
        if ./scripts/docker-build.sh pharos; then
            log_success "Pharos 镜像构建完成"
        else
            log_error "Pharos 镜像构建失败"
            return 1
        fi
    fi
}

# 优雅停止和更新容器
update_containers() {
    log_step "更新容器..."
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        log_info "更新 Kaia 容器..."
        
        # 优雅停止容器
        if docker ps -q -f name=moofun-kaia-container | grep -q .; then
            log_info "停止 Kaia 容器..."
            docker stop moofun-kaia-container
            docker rm moofun-kaia-container
        fi
        
        # 启动新容器
        log_info "启动新 Kaia 容器..."
        if docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia; then
            log_success "Kaia 容器启动成功"
        else
            log_error "Kaia 容器启动失败"
            return 1
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        log_info "更新 Pharos 容器..."
        
        # 优雅停止容器
        if docker ps -q -f name=moofun-pharos-container | grep -q .; then
            log_info "停止 Pharos 容器..."
            docker stop moofun-pharos-container
            docker rm moofun-pharos-container
        fi
        
        # 启动新容器
        log_info "启动新 Pharos 容器..."
        if docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos; then
            log_success "Pharos 容器启动成功"
        else
            log_error "Pharos 容器启动失败"
            return 1
        fi
    fi
}

# 验证服务健康状态
verify_services() {
    log_step "验证服务健康状态..."
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    local all_healthy=true
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        log_info "检查 Kaia API 健康状态..."
        local attempts=0
        local max_attempts=6
        
        while [ $attempts -lt $max_attempts ]; do
            if curl -f http://localhost:9112/api/health/health &>/dev/null; then
                log_success "Kaia API 健康检查通过"
                break
            else
                ((attempts++))
                if [ $attempts -eq $max_attempts ]; then
                    log_error "Kaia API 健康检查失败"
                    all_healthy=false
                else
                    log_info "Kaia API 未就绪，等待 10 秒后重试... ($attempts/$max_attempts)"
                    sleep 10
                fi
            fi
        done
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        log_info "检查 Pharos API 健康状态..."
        local attempts=0
        local max_attempts=6
        
        while [ $attempts -lt $max_attempts ]; do
            if curl -f http://localhost:9113/api/health/health &>/dev/null; then
                log_success "Pharos API 健康检查通过"
                break
            else
                ((attempts++))
                if [ $attempts -eq $max_attempts ]; then
                    log_error "Pharos API 健康检查失败"
                    all_healthy=false
                else
                    log_info "Pharos API 未就绪，等待 10 秒后重试... ($attempts/$max_attempts)"
                    sleep 10
                fi
            fi
        done
    fi
    
    if [ "$all_healthy" = false ]; then
        return 1
    fi
    
    log_success "所有服务健康检查通过"
    return 0
}

# 回滚到之前版本
rollback_services() {
    log_warning "开始回滚服务..."

    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        if [ -f ".last_kaia_backup" ]; then
            local backup_image=$(cat .last_kaia_backup)
            log_info "回滚 Kaia 到备份镜像: $backup_image"

            # 停止当前容器
            docker stop moofun-kaia-container 2>/dev/null || true
            docker rm moofun-kaia-container 2>/dev/null || true

            # 使用备份镜像启动容器
            docker tag "$backup_image" moofun-kaia:latest
            docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia

            log_success "Kaia 服务回滚完成"
        else
            log_warning "未找到 Kaia 备份镜像"
        fi
    fi

    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        if [ -f ".last_pharos_backup" ]; then
            local backup_image=$(cat .last_pharos_backup)
            log_info "回滚 Pharos 到备份镜像: $backup_image"

            # 停止当前容器
            docker stop moofun-pharos-container 2>/dev/null || true
            docker rm moofun-pharos-container 2>/dev/null || true

            # 使用备份镜像启动容器
            docker tag "$backup_image" moofun-pharos:latest
            docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos

            log_success "Pharos 服务回滚完成"
        else
            log_warning "未找到 Pharos 备份镜像"
        fi
    fi

    # 回滚代码
    if [ -f ".last_deploy_commit" ] && [ "$SKIP_GIT" != true ]; then
        local last_commit=$(cat .last_deploy_commit)
        log_info "回滚代码到提交: ${last_commit:0:8}"
        git reset --hard "$last_commit"
        log_success "代码回滚完成"
    fi
}

# 清理备份文件
cleanup_backups() {
    log_step "清理旧备份..."

    # 保留最近3个备份镜像
    local kaia_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | sort -r | tail -n +4)
    local pharos_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | sort -r | tail -n +4)

    if [ -n "$kaia_backups" ]; then
        echo "$kaia_backups" | xargs -r docker rmi
        log_info "清理了旧的 Kaia 备份镜像"
    fi

    if [ -n "$pharos_backups" ]; then
        echo "$pharos_backups" | xargs -r docker rmi
        log_info "清理了旧的 Pharos 备份镜像"
    fi
}

# 显示更新结果
show_update_result() {
    echo ""
    echo "🎉 代码更新完成！"
    echo ""
    echo "📊 服务状态:"

    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        echo "  🌐 Kaia API: http://localhost:9112/"
        echo "  🔍 健康检查: http://localhost:9112/api/health/health"
    fi

    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        echo "  🌐 Pharos API: http://localhost:9113/"
        echo "  🔍 健康检查: http://localhost:9113/api/health/health"
    fi

    echo ""
    echo "🐳 容器管理命令:"
    echo "  📊 查看日志: docker logs -f moofun-kaia-container"
    echo "  📊 查看日志: docker logs -f moofun-pharos-container"
    echo "  🔄 重启服务: docker restart moofun-kaia-container moofun-pharos-container"
    echo ""
    echo "🔙 如需回滚，请运行: ./rollback.sh"
}

# 解析命令行参数
SERVICE="both"
SKIP_GIT=false
SKIP_BACKUP=false
NO_ROLLBACK=false
FORCE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            SERVICE="$1"
            shift
            ;;
        --skip-git)
            SKIP_GIT=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --no-rollback)
            NO_ROLLBACK=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 代码更新部署"
    log_info "更新服务: $SERVICE"

    # Dry run 模式
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式：显示将要执行的操作"
        echo ""
        echo "将执行以下步骤："
        [ "$SKIP_GIT" != true ] && echo "  ✅ 拉取最新代码"
        [ "$SKIP_BACKUP" != true ] && echo "  ✅ 备份当前镜像"
        echo "  ✅ 构建新镜像"
        echo "  ✅ 更新容器"
        echo "  ✅ 验证服务健康状态"
        [ "$NO_ROLLBACK" != true ] && echo "  ✅ 失败时自动回滚"
        echo ""
        check_current_status
        log_success "🔍 Dry Run 完成"
        exit 0
    fi

    # 执行更新步骤
    check_prerequisites
    check_current_status

    # 确认更新
    if [ "$FORCE" != true ]; then
        echo ""
        read -p "确认开始更新 $SERVICE 服务？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消更新"
            exit 0
        fi
    fi

    # 记录开始时间
    local start_time=$(date +%s)

    # 执行更新流程
    if pull_latest_code && \
       backup_images && \
       build_new_images && \
       update_containers && \
       verify_services; then

        # 更新成功
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        cleanup_backups
        show_update_result

        log_success "🎉 更新完成！耗时: ${duration}秒"

    else
        # 更新失败，执行回滚
        log_error "更新失败！"

        if [ "$NO_ROLLBACK" != true ]; then
            log_warning "开始自动回滚..."
            rollback_services

            # 验证回滚结果
            sleep 10
            if verify_services; then
                log_success "回滚成功，服务已恢复"
            else
                log_error "回滚失败，请手动检查服务状态"
            fi
        else
            log_warning "已禁用自动回滚，请手动处理"
        fi

        exit 1
    fi
}

# 运行主函数
main
