#!/bin/bash

# Wolf Fun 代码更新部署脚本
# 用于服务器生产环境的快速代码更新，保持数据安全，最小化停机时间

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Wolf Fun 代码更新部署脚本${NC}"
    echo ""
    echo "用法: $0 [服务] [选项]"
    echo ""
    echo "服务:"
    echo "  kaia      只更新 Kaia API 服务"
    echo "  pharos    只更新 Pharos API 服务"
    echo "  both      更新两个服务（默认）"
    echo ""
    echo "选项:"
    echo "  --skip-git       跳过 Git 代码拉取"
    echo "  --skip-backup    跳过镜像备份"
    echo "  --skip-cleanup   跳过 Docker 镜像清理"
    echo "  --no-rollback    禁用自动回滚功能"
    echo "  --force          强制更新（跳过确认）"
    echo "  --dry-run        只显示将要执行的操作"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                       # 更新两个服务"
    echo "  $0 kaia                  # 只更新 Kaia 服务"
    echo "  $0 both --skip-git       # 更新两个服务但跳过代码拉取"
    echo "  $0 pharos --skip-cleanup # 更新 Pharos 但跳过镜像清理"
    echo "  $0 pharos --dry-run      # 预览 Pharos 更新操作"
}

# 检查 Docker 和 Git
check_prerequisites() {
    log_step "检查系统环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 检查当前服务状态
check_current_status() {
    log_step "检查当前服务状态..."
    
    local kaia_status=$(docker inspect --format='{{.State.Status}}' moofun-kaia-container 2>/dev/null || echo "not_found")
    local pharos_status=$(docker inspect --format='{{.State.Status}}' moofun-pharos-container 2>/dev/null || echo "not_found")
    
    echo "📊 当前服务状态:"
    echo "  🔹 Kaia 容器: $kaia_status"
    echo "  🔹 Pharos 容器: $pharos_status"
    
    # 检查健康状态
    if [ "$kaia_status" = "running" ]; then
        if curl -f http://localhost:9112/api/health/health &>/dev/null; then
            echo "  ✅ Kaia API: 健康"
        else
            echo "  ❌ Kaia API: 不健康"
        fi
    fi
    
    if [ "$pharos_status" = "running" ]; then
        if curl -f http://localhost:9113/api/health/health &>/dev/null; then
            echo "  ✅ Pharos API: 健康"
        else
            echo "  ❌ Pharos API: 不健康"
        fi
    fi
}

# 拉取最新代码
pull_latest_code() {
    if [ "$SKIP_GIT" = true ]; then
        log_warning "跳过 Git 代码拉取"
        return 0
    fi
    
    log_step "拉取最新代码..."
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_warning "检测到未提交的更改"
        if [ "$FORCE" != true ]; then
            read -p "是否继续？这可能会覆盖本地更改 (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "取消更新"
                exit 0
            fi
        fi
    fi
    
    # 获取当前分支和提交
    local current_branch=$(git branch --show-current)
    local current_commit=$(git rev-parse HEAD)
    
    log_info "当前分支: $current_branch"
    log_info "当前提交: ${current_commit:0:8}"
    
    # 拉取最新代码
    if git pull origin "$current_branch"; then
        local new_commit=$(git rev-parse HEAD)
        if [ "$current_commit" = "$new_commit" ]; then
            log_info "代码已是最新版本"
        else
            log_success "代码更新完成: ${current_commit:0:8} -> ${new_commit:0:8}"
            # 保存提交信息用于回滚
            echo "$current_commit" > .last_deploy_commit
        fi
    else
        log_error "代码拉取失败"
        exit 1
    fi
}

# 备份当前镜像
backup_images() {
    if [ "$SKIP_BACKUP" = true ]; then
        log_warning "跳过镜像备份"
        return 0
    fi
    
    log_step "备份当前镜像..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        if docker images -q moofun-kaia:latest &>/dev/null; then
            docker tag moofun-kaia:latest moofun-kaia:backup_$timestamp
            log_success "Kaia 镜像备份完成: moofun-kaia:backup_$timestamp"
            echo "moofun-kaia:backup_$timestamp" > .last_kaia_backup
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        if docker images -q moofun-pharos:latest &>/dev/null; then
            docker tag moofun-pharos:latest moofun-pharos:backup_$timestamp
            log_success "Pharos 镜像备份完成: moofun-pharos:backup_$timestamp"
            echo "moofun-pharos:backup_$timestamp" > .last_pharos_backup
        fi
    fi
}

# 构建新镜像
build_new_images() {
    log_step "构建新镜像..."
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        log_info "构建 Kaia 镜像..."
        if ./scripts/docker-build.sh kaia; then
            log_success "Kaia 镜像构建完成"
        else
            log_error "Kaia 镜像构建失败"
            return 1
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        log_info "构建 Pharos 镜像..."
        if ./scripts/docker-build.sh pharos; then
            log_success "Pharos 镜像构建完成"
        else
            log_error "Pharos 镜像构建失败"
            return 1
        fi
    fi
}

# 优雅停止和更新容器
update_containers() {
    log_step "更新容器..."
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        log_info "更新 Kaia 容器..."
        
        # 优雅停止容器
        if docker ps -q -f name=moofun-kaia-container | grep -q .; then
            log_info "停止 Kaia 容器..."
            docker stop moofun-kaia-container
            docker rm moofun-kaia-container
        fi
        
        # 启动新容器
        log_info "启动新 Kaia 容器..."
        if docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia; then
            log_success "Kaia 容器启动成功"
        else
            log_error "Kaia 容器启动失败"
            return 1
        fi
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        log_info "更新 Pharos 容器..."
        
        # 优雅停止容器
        if docker ps -q -f name=moofun-pharos-container | grep -q .; then
            log_info "停止 Pharos 容器..."
            docker stop moofun-pharos-container
            docker rm moofun-pharos-container
        fi
        
        # 启动新容器
        log_info "启动新 Pharos 容器..."
        if docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos; then
            log_success "Pharos 容器启动成功"
        else
            log_error "Pharos 容器启动失败"
            return 1
        fi
    fi
}

# 验证服务健康状态
verify_services() {
    log_step "验证服务健康状态..."
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    local all_healthy=true
    
    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        log_info "检查 Kaia API 健康状态..."
        local attempts=0
        local max_attempts=6
        
        while [ $attempts -lt $max_attempts ]; do
            if curl -f http://localhost:9112/api/health/health &>/dev/null; then
                log_success "Kaia API 健康检查通过"
                break
            else
                ((attempts++))
                if [ $attempts -eq $max_attempts ]; then
                    log_error "Kaia API 健康检查失败"
                    all_healthy=false
                else
                    log_info "Kaia API 未就绪，等待 10 秒后重试... ($attempts/$max_attempts)"
                    sleep 10
                fi
            fi
        done
    fi
    
    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        log_info "检查 Pharos API 健康状态..."
        local attempts=0
        local max_attempts=6
        
        while [ $attempts -lt $max_attempts ]; do
            if curl -f http://localhost:9113/api/health/health &>/dev/null; then
                log_success "Pharos API 健康检查通过"
                break
            else
                ((attempts++))
                if [ $attempts -eq $max_attempts ]; then
                    log_error "Pharos API 健康检查失败"
                    all_healthy=false
                else
                    log_info "Pharos API 未就绪，等待 10 秒后重试... ($attempts/$max_attempts)"
                    sleep 10
                fi
            fi
        done
    fi
    
    if [ "$all_healthy" = false ]; then
        return 1
    fi
    
    log_success "所有服务健康检查通过"
    return 0
}

# 回滚到之前版本
rollback_services() {
    log_warning "开始回滚服务..."

    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        if [ -f ".last_kaia_backup" ]; then
            local backup_image=$(cat .last_kaia_backup)
            log_info "回滚 Kaia 到备份镜像: $backup_image"

            # 停止当前容器
            docker stop moofun-kaia-container 2>/dev/null || true
            docker rm moofun-kaia-container 2>/dev/null || true

            # 使用备份镜像启动容器
            docker tag "$backup_image" moofun-kaia:latest
            docker run -d -p 9112:3456 --name moofun-kaia-container --network moofun moofun-kaia

            log_success "Kaia 服务回滚完成"
        else
            log_warning "未找到 Kaia 备份镜像"
        fi
    fi

    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        if [ -f ".last_pharos_backup" ]; then
            local backup_image=$(cat .last_pharos_backup)
            log_info "回滚 Pharos 到备份镜像: $backup_image"

            # 停止当前容器
            docker stop moofun-pharos-container 2>/dev/null || true
            docker rm moofun-pharos-container 2>/dev/null || true

            # 使用备份镜像启动容器
            docker tag "$backup_image" moofun-pharos:latest
            docker run -d -p 9113:3457 --name moofun-pharos-container --network moofun moofun-pharos

            log_success "Pharos 服务回滚完成"
        else
            log_warning "未找到 Pharos 备份镜像"
        fi
    fi

    # 回滚代码
    if [ -f ".last_deploy_commit" ] && [ "$SKIP_GIT" != true ]; then
        local last_commit=$(cat .last_deploy_commit)
        log_info "回滚代码到提交: ${last_commit:0:8}"
        git reset --hard "$last_commit"
        log_success "代码回滚完成"
    fi
}

# 显示磁盘使用情况
show_disk_usage() {
    local title="$1"
    log_info "$title"

    # 显示 Docker 系统使用情况
    echo "🐳 Docker 系统使用情况:"
    docker system df

    # 显示镜像数量统计
    local total_images=$(docker images -q | wc -l)
    local dangling_images=$(docker images -f "dangling=true" -q | wc -l)
    local kaia_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "moofun-kaia" || echo "0")
    local pharos_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -c "moofun-pharos" || echo "0")

    echo "📊 镜像统计:"
    echo "  总镜像数: $total_images"
    echo "  悬空镜像: $dangling_images"
    echo "  Kaia 镜像: $kaia_images"
    echo "  Pharos 镜像: $pharos_images"
    echo ""
}

# 检查回滚备份镜像是否存在
check_rollback_safety() {
    log_info "检查回滚安全性..."

    local rollback_safe=true

    # 检查 Kaia 回滚备份
    if [ -f ".last_kaia_backup" ]; then
        local kaia_backup=$(cat .last_kaia_backup)
        if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$kaia_backup$"; then
            log_warning "Kaia 回滚备份镜像不存在: $kaia_backup"
            rollback_safe=false
        else
            log_info "✅ Kaia 回滚备份镜像存在: $kaia_backup"
        fi
    fi

    # 检查 Pharos 回滚备份
    if [ -f ".last_pharos_backup" ]; then
        local pharos_backup=$(cat .last_pharos_backup)
        if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$pharos_backup$"; then
            log_warning "Pharos 回滚备份镜像不存在: $pharos_backup"
            rollback_safe=false
        else
            log_info "✅ Pharos 回滚备份镜像存在: $pharos_backup"
        fi
    fi

    if [ "$rollback_safe" = false ]; then
        log_warning "⚠️  回滚备份镜像缺失，建议跳过清理或重新创建备份"
        return 1
    fi

    log_success "✅ 回滚安全检查通过"
    return 0
}

# 清理 Docker 镜像和缓存
cleanup_docker_images() {
    log_step "清理 Docker 镜像和缓存..."

    # 检查回滚安全性
    if ! check_rollback_safety; then
        log_warning "回滚安全检查失败，是否继续清理？"
        read -p "继续清理可能影响回滚功能 (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消清理以保护回滚功能"
            return 0
        fi
    fi

    # 显示清理前的磁盘使用情况
    show_disk_usage "🔍 清理前磁盘使用情况:"

    # 1. 清理悬空镜像（dangling images）
    log_info "清理悬空镜像..."
    local dangling_images=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling_images" ]; then
        echo "$dangling_images" | xargs docker rmi 2>/dev/null || true
        log_success "悬空镜像清理完成"
    else
        log_info "没有悬空镜像需要清理"
    fi

    # 2. 清理旧版本的应用镜像（保留最近3个版本 + 当前回滚备份）
    log_info "清理旧版本应用镜像..."

    # 获取当前回滚备份镜像（需要特别保护）
    local current_kaia_backup=""
    local current_pharos_backup=""

    if [ -f ".last_kaia_backup" ]; then
        current_kaia_backup=$(cat .last_kaia_backup)
        log_info "保护当前 Kaia 回滚备份: $current_kaia_backup"
    fi

    if [ -f ".last_pharos_backup" ]; then
        current_pharos_backup=$(cat .last_pharos_backup)
        log_info "保护当前 Pharos 回滚备份: $current_pharos_backup"
    fi

    # 清理 Kaia 镜像（保留 latest、最近3个备份、当前回滚备份）
    local kaia_all_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | sort -r)
    local kaia_to_keep=$(echo "$kaia_all_backups" | head -n 3)

    # 如果当前回滚备份不在最近3个中，也要保留
    if [ -n "$current_kaia_backup" ] && ! echo "$kaia_to_keep" | grep -q "$current_kaia_backup"; then
        kaia_to_keep="$kaia_to_keep"$'\n'"$current_kaia_backup"
    fi

    local kaia_old_images=$(echo "$kaia_all_backups" | grep -v -F "$kaia_to_keep" || true)
    if [ -n "$kaia_old_images" ]; then
        echo "$kaia_old_images" | xargs -r docker rmi 2>/dev/null || true
        log_info "清理了旧的 Kaia 镜像（保留了回滚备份）"
    fi

    # 清理 Pharos 镜像（保留 latest、最近3个备份、当前回滚备份）
    local pharos_all_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | sort -r)
    local pharos_to_keep=$(echo "$pharos_all_backups" | head -n 3)

    # 如果当前回滚备份不在最近3个中，也要保留
    if [ -n "$current_pharos_backup" ] && ! echo "$pharos_to_keep" | grep -q "$current_pharos_backup"; then
        pharos_to_keep="$pharos_to_keep"$'\n'"$current_pharos_backup"
    fi

    local pharos_old_images=$(echo "$pharos_all_backups" | grep -v -F "$pharos_to_keep" || true)
    if [ -n "$pharos_old_images" ]; then
        echo "$pharos_old_images" | xargs -r docker rmi 2>/dev/null || true
        log_info "清理了旧的 Pharos 镜像（保留了回滚备份）"
    fi

    # 3. 清理未使用的镜像（不删除正在使用的）
    log_info "清理未使用的镜像..."
    local unused_images=$(docker images --filter "dangling=false" --format "{{.ID}} {{.Repository}}:{{.Tag}}" | \
        grep -v "moofun-kaia:latest\|moofun-pharos:latest\|moofun-kaia:backup_\|moofun-pharos:backup_" | \
        grep -E "^[a-f0-9]{12} (moofun-kaia|moofun-pharos):" | \
        awk '{print $1}' | sort -u)

    if [ -n "$unused_images" ]; then
        # 检查这些镜像是否被容器使用
        for image_id in $unused_images; do
            local containers_using_image=$(docker ps -a --filter "ancestor=$image_id" -q)
            if [ -z "$containers_using_image" ]; then
                docker rmi "$image_id" 2>/dev/null || true
            fi
        done
        log_info "清理了未使用的应用镜像"
    fi

    # 4. 清理构建缓存
    log_info "清理构建缓存..."
    docker builder prune -f >/dev/null 2>&1 || true
    log_success "构建缓存清理完成"

    # 5. 清理未使用的网络和卷（谨慎操作）
    log_info "清理未使用的网络..."
    docker network prune -f >/dev/null 2>&1 || true

    # 显示清理后的磁盘使用情况
    show_disk_usage "✅ 清理后磁盘使用情况:"

    log_success "Docker 镜像和缓存清理完成"
}

# 清理备份文件（保留原有功能，但现在被 cleanup_docker_images 包含）
cleanup_backups() {
    log_step "清理旧备份..."

    # 保留最近3个备份镜像
    local kaia_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-kaia:backup_" | sort -r | tail -n +4)
    local pharos_backups=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "moofun-pharos:backup_" | sort -r | tail -n +4)

    if [ -n "$kaia_backups" ]; then
        echo "$kaia_backups" | xargs -r docker rmi
        log_info "清理了旧的 Kaia 备份镜像"
    fi

    if [ -n "$pharos_backups" ]; then
        echo "$pharos_backups" | xargs -r docker rmi
        log_info "清理了旧的 Pharos 备份镜像"
    fi
}

# 显示更新结果
show_update_result() {
    echo ""
    echo "🎉 代码更新完成！"
    echo ""
    echo "📊 服务状态:"

    if [ "$SERVICE" = "kaia" ] || [ "$SERVICE" = "both" ]; then
        echo "  🌐 Kaia API: http://localhost:9112/"
        echo "  🔍 健康检查: http://localhost:9112/api/health/health"
    fi

    if [ "$SERVICE" = "pharos" ] || [ "$SERVICE" = "both" ]; then
        echo "  🌐 Pharos API: http://localhost:9113/"
        echo "  🔍 健康检查: http://localhost:9113/api/health/health"
    fi

    echo ""
    echo "🐳 容器管理命令:"
    echo "  📊 查看日志: docker logs -f moofun-kaia-container"
    echo "  📊 查看日志: docker logs -f moofun-pharos-container"
    echo "  🔄 重启服务: docker restart moofun-kaia-container moofun-pharos-container"
    echo ""
    echo "🧹 清理管理命令:"
    echo "  🗑️  手动清理: ./cleanup-docker.sh"
    echo "  📊 查看磁盘: docker system df"
    echo ""
    echo "🔙 如需回滚，请运行: ./rollback.sh"
}

# 解析命令行参数
SERVICE="both"
SKIP_GIT=false
SKIP_BACKUP=false
SKIP_CLEANUP=false
NO_ROLLBACK=false
FORCE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            SERVICE="$1"
            shift
            ;;
        --skip-git)
            SKIP_GIT=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-cleanup)
            SKIP_CLEANUP=true
            shift
            ;;
        --no-rollback)
            NO_ROLLBACK=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 代码更新部署"
    log_info "更新服务: $SERVICE"

    # Dry run 模式
    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 Dry Run 模式：显示将要执行的操作"
        echo ""
        echo "将执行以下步骤："
        [ "$SKIP_GIT" != true ] && echo "  ✅ 拉取最新代码"
        [ "$SKIP_BACKUP" != true ] && echo "  ✅ 备份当前镜像"
        echo "  ✅ 构建新镜像"
        echo "  ✅ 更新容器"
        echo "  ✅ 验证服务健康状态"
        [ "$SKIP_CLEANUP" != true ] && echo "  ✅ 清理 Docker 镜像和缓存"
        [ "$NO_ROLLBACK" != true ] && echo "  ✅ 失败时自动回滚"
        echo ""
        check_current_status
        log_success "🔍 Dry Run 完成"
        exit 0
    fi

    # 执行更新步骤
    check_prerequisites
    check_current_status

    # 确认更新
    if [ "$FORCE" != true ]; then
        echo ""
        read -p "确认开始更新 $SERVICE 服务？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消更新"
            exit 0
        fi
    fi

    # 记录开始时间
    local start_time=$(date +%s)

    # 执行更新流程
    if pull_latest_code && \
       backup_images && \
       build_new_images && \
       update_containers && \
       verify_services; then

        # 更新成功
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        show_update_result

        # 清理 Docker 镜像和缓存（在显示结果后执行，确保用户知道更新成功）
        if [ "$SKIP_CLEANUP" != true ]; then
            log_info "等待5秒后开始清理（如需回滚请按 Ctrl+C 取消）..."
            sleep 5
            cleanup_docker_images
        else
            log_warning "跳过 Docker 镜像清理"
        fi

        log_success "🎉 更新完成！耗时: ${duration}秒"

    else
        # 更新失败，执行回滚
        log_error "更新失败！"

        if [ "$NO_ROLLBACK" != true ]; then
            log_warning "开始自动回滚..."
            rollback_services

            # 验证回滚结果
            sleep 10
            if verify_services; then
                log_success "回滚成功，服务已恢复"
            else
                log_error "回滚失败，请手动检查服务状态"
            fi
        else
            log_warning "已禁用自动回滚，请手动处理"
        fi

        exit 1
    fi
}

# 运行主函数
main
